1.文件说明：
    - 场站POI数据_valid：已有的场站基础信息与通过高德API获取的POIxinxi
    - 高德POI分类与编码（中英文）_V1.06_20230208：高德官方POI分类与编码
        分类代码由六位数字组成，一共分为三个部分，前两个数字代表大类；中间两个数字代表中类；最后两个数字代表小类。若指定了某个大类，则所属的中类、小类都会被显示。
        例如：010000为汽车服务（大类）
          010100为加油站（中类）
          010101为中国石化（小类）
          010900为汽车租赁（中类）
          010901为汽车租赁还车（小类）
        当指定010000，则010100等中类、010101等小类会被包含，当指定010900，则010901等小类会被包含。
    - 使用POI：决定使用的POI编码（其中有大类、中类和小类），希望以这个为基础修改“场站POI数据_valid”内的数据，做数量累计，保留POI的类别和数量
    - 新建场站决策辅助功能：整体方案文件
    - raw_poi_data:通过高德API获取的每个场站3千米内的POI数据

2.工作内容：
    请以“新建场站决策辅助功能”为基础开发，目前只做其中第一阶段的内容。但是由于缺少数据，现在只有POI信息可用，因此是否可以考虑在得到新的场站的坐标时，获取附近POI，进行相似度计算，依据相似的场站情况再推测是否合适新建场站。
    由于目前处于开发阶段一，请注意“多维度评分框架”的构建，并给出说明和依据，以及需要考虑到后续的开发要留出延伸空间。后续可补充的信息有：1.基于坐标获取的附近人口数量信息 2.现有场站的充电订单信息：包括充电用户，场站，订单编号，充电时长、使用电量，订单金额，开始时间，结束时间，开始SOC
