#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于距离权重的POI计算器
实现距离衰减的POI评分方法，距离越近的POI权重越大
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from collections import defaultdict
from station_analysis import StationAnalyzer

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

class DistanceWeightedPOICalculator:
    """基于距离权重的POI计算器"""
    
    def __init__(self, decay_factor=1000, max_distance=3000, min_weight=0.1):
        """初始化距离权重计算器
        
        参数:
            decay_factor: 距离衰减因子（米），值越小衰减越快
            max_distance: 最大有效距离（米），超过此距离的POI权重为min_weight
            min_weight: 最小权重值
        """
        self.decay_factor = decay_factor
        self.max_distance = max_distance
        self.min_weight = min_weight
        self.analyzer = None
        self.distance_weighted_vectors = None
        self.original_vectors = None
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.analyzer = StationAnalyzer()
        self.analyzer.load_data()
        print("数据加载完成")
    
    def calculate_distance_weight(self, distance):
        """计算距离权重
        
        参数:
            distance: 距离（米）
            
        返回:
            距离权重 (min_weight ~ 1.0)
        """
        if distance > self.max_distance:
            return self.min_weight
        
        # 使用指数衰减函数：weight = exp(-distance/decay_factor)
        weight = np.exp(-distance / self.decay_factor)
        
        # 确保权重不低于最小值
        return max(self.min_weight, weight)
    
    def build_distance_weighted_vectors(self):
        """构建基于距离权重的POI向量"""
        if self.analyzer is None:
            self.load_data()
            
        print("正在构建基于距离权重的POI向量...")
        
        # 创建一个字典，用于存储每个场站的POI向量
        station_poi_vectors = {}
        
        # 对于每个场站
        for station_name, station_info in self.analyzer.raw_poi_data.items():
            # 初始化POI向量
            poi_vector = defaultdict(float)  # 使用float支持小数权重
            
            # 获取场站周围的POI数据
            pois = station_info['data'].get('pois', [])
            
            # 统计每个POI类别的加权数量
            for poi in pois:
                if 'typecode' in poi and 'distance' in poi:
                    typecode = poi['typecode']
                    distance = float(poi.get('distance', 0))
                    
                    # 计算距离权重
                    distance_weight = self.calculate_distance_weight(distance)
                    
                    # 获取大类和中类编码
                    if len(typecode) == 6:  # 确保是6位编码
                        large_category = typecode[:2] + '0000'  # 大类
                        medium_category = typecode[:4] + '00'   # 中类
                        
                        # 优先使用最具体的编码，避免重复计算（结合之前的改进）
                        if typecode in self.analyzer.used_poi_codes:
                            poi_vector[typecode] += distance_weight
                        elif medium_category in self.analyzer.used_poi_codes:
                            poi_vector[medium_category] += distance_weight
                        elif large_category in self.analyzer.used_poi_codes:
                            poi_vector[large_category] += distance_weight
            
            # 将defaultdict转换为普通dict并存储
            station_poi_vectors[station_name] = dict(poi_vector)
        
        # 创建一个DataFrame，行是场站，列是POI类别
        all_typecodes = sorted(self.analyzer.used_poi_codes)
        stations = sorted(station_poi_vectors.keys())
        
        # 创建一个空的DataFrame
        self.distance_weighted_vectors = pd.DataFrame(0.0, index=stations, columns=all_typecodes)
        
        # 填充DataFrame
        for station in stations:
            for typecode, weighted_count in station_poi_vectors[station].items():
                if typecode in all_typecodes:
                    self.distance_weighted_vectors.at[station, typecode] = weighted_count
        
        print(f"已为 {len(stations)} 个场站构建距离权重POI向量，包含 {len(all_typecodes)} 个POI类别")
        
        return self.distance_weighted_vectors
    
    def build_original_vectors(self):
        """构建原有的POI向量（不考虑距离）"""
        if self.analyzer is None:
            self.load_data()
            
        print("正在构建原有的POI向量（不考虑距离）...")
        
        # 使用改进的方法构建原有向量（避免重复计算但不考虑距离）
        station_poi_vectors = {}
        
        for station_name, station_info in self.analyzer.raw_poi_data.items():
            poi_vector = defaultdict(int)
            pois = station_info['data'].get('pois', [])
            
            for poi in pois:
                if 'typecode' in poi:
                    typecode = poi['typecode']
                    
                    if len(typecode) == 6:
                        large_category = typecode[:2] + '0000'
                        medium_category = typecode[:4] + '00'
                        
                        # 不考虑距离，只计数
                        if typecode in self.analyzer.used_poi_codes:
                            poi_vector[typecode] += 1
                        elif medium_category in self.analyzer.used_poi_codes:
                            poi_vector[medium_category] += 1
                        elif large_category in self.analyzer.used_poi_codes:
                            poi_vector[large_category] += 1
            
            station_poi_vectors[station_name] = dict(poi_vector)
        
        # 创建DataFrame
        all_typecodes = sorted(self.analyzer.used_poi_codes)
        stations = sorted(station_poi_vectors.keys())
        
        self.original_vectors = pd.DataFrame(0, index=stations, columns=all_typecodes)
        
        for station in stations:
            for typecode, count in station_poi_vectors[station].items():
                if typecode in all_typecodes:
                    self.original_vectors.at[station, typecode] = count
        
        return self.original_vectors
    
    def analyze_distance_distribution(self):
        """分析POI距离分布"""
        print("\n分析POI距离分布...")
        
        all_distances = []
        distance_by_type = defaultdict(list)
        
        for station_name, station_info in self.analyzer.raw_poi_data.items():
            pois = station_info['data'].get('pois', [])
            for poi in pois:
                if 'distance' in poi and 'typecode' in poi:
                    distance = float(poi['distance'])
                    typecode = poi['typecode']
                    all_distances.append(distance)
                    
                    # 按大类统计
                    if len(typecode) >= 2:
                        large_category = typecode[:2]
                        distance_by_type[large_category].append(distance)
        
        # 统计信息
        distances_array = np.array(all_distances)
        print(f"POI距离统计:")
        print(f"  总数量: {len(all_distances)}")
        print(f"  平均距离: {distances_array.mean():.0f}米")
        print(f"  中位数距离: {np.median(distances_array):.0f}米")
        print(f"  最大距离: {distances_array.max():.0f}米")
        print(f"  最小距离: {distances_array.min():.0f}米")
        
        # 距离分布
        print(f"\n距离分布:")
        bins = [0, 200, 500, 1000, 2000, 3000, float('inf')]
        labels = ['0-200m', '200-500m', '500-1000m', '1000-2000m', '2000-3000m', '>3000m']
        
        for i in range(len(bins)-1):
            count = np.sum((distances_array >= bins[i]) & (distances_array < bins[i+1]))
            percentage = count / len(all_distances) * 100
            print(f"  {labels[i]}: {count} ({percentage:.1f}%)")
        
        return all_distances, distance_by_type
    
    def visualize_distance_weights(self):
        """可视化距离权重函数"""
        distances = np.arange(0, 4000, 50)
        weights = [self.calculate_distance_weight(d) for d in distances]
        
        plt.figure(figsize=(10, 6))
        plt.plot(distances, weights, 'b-', linewidth=2, label=f'衰减因子={self.decay_factor}m')
        plt.axhline(y=self.min_weight, color='r', linestyle='--', alpha=0.7, label=f'最小权重={self.min_weight}')
        plt.axvline(x=self.max_distance, color='g', linestyle='--', alpha=0.7, label=f'最大距离={self.max_distance}m')
        
        plt.xlabel('距离 (米)')
        plt.ylabel('权重')
        plt.title('距离权重衰减函数')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xlim(0, 4000)
        plt.ylim(0, 1.1)
        
        # 添加关键点标注
        key_distances = [200, 500, 1000, 2000]
        for d in key_distances:
            w = self.calculate_distance_weight(d)
            plt.plot(d, w, 'ro', markersize=6)
            plt.annotate(f'{d}m: {w:.2f}', (d, w), xytext=(10, 10), 
                        textcoords='offset points', fontsize=9)
        
        plt.tight_layout()
        plt.savefig('output/distance_weight_function.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("距离权重函数图已保存到 output/distance_weight_function.png")
    
    def compare_methods(self):
        """对比原有方法和距离权重方法"""
        print("\n开始对比原有方法和距离权重方法...")
        
        # 构建两种向量
        if self.original_vectors is None:
            self.build_original_vectors()
        if self.distance_weighted_vectors is None:
            self.build_distance_weighted_vectors()
        
        # 分析距离分布
        self.analyze_distance_distribution()
        
        # 可视化距离权重函数
        self.visualize_distance_weights()
        
        # 对比评分效果
        self._compare_scoring_effects()
        
        # 生成对比报告
        self._generate_comparison_report()
    
    def _compare_scoring_effects(self):
        """对比评分效果"""
        print("\n对比评分效果...")
        
        # 计算原有方法的评分（使用原有向量）
        original_scores = []
        for station in self.original_vectors.index:
            # 临时替换POI向量
            original_poi_vectors = self.analyzer.poi_vectors
            self.analyzer.poi_vectors = self.original_vectors
            score = self.analyzer.evaluate_station_score(station)
            original_scores.append(score)
            # 恢复
            self.analyzer.poi_vectors = original_poi_vectors
        
        # 计算距离权重方法的评分
        distance_weighted_scores = []
        for station in self.distance_weighted_vectors.index:
            if station in self.original_vectors.index:
                # 临时替换POI向量
                original_poi_vectors = self.analyzer.poi_vectors
                self.analyzer.poi_vectors = self.distance_weighted_vectors
                score = self.analyzer.evaluate_station_score(station)
                distance_weighted_scores.append(score)
                # 恢复
                self.analyzer.poi_vectors = original_poi_vectors
            else:
                distance_weighted_scores.append(0)
        
        # 计算统计指标
        original_stats = pd.Series(original_scores).describe()
        distance_stats = pd.Series(distance_weighted_scores).describe()
        
        print("评分统计对比:")
        print(f"{'指标':<10} {'原有方法':<12} {'距离权重':<12} {'变化':<10}")
        print("-" * 50)
        print(f"{'平均分':<10} {original_stats['mean']:<12.2f} {distance_stats['mean']:<12.2f} {(distance_stats['mean']-original_stats['mean'])/original_stats['mean']*100:>8.1f}%")
        print(f"{'标准差':<10} {original_stats['std']:<12.2f} {distance_stats['std']:<12.2f} {(distance_stats['std']-original_stats['std'])/original_stats['std']*100:>8.1f}%")
        print(f"{'最大值':<10} {original_stats['max']:<12.2f} {distance_stats['max']:<12.2f} {(distance_stats['max']-original_stats['max'])/original_stats['max']*100:>8.1f}%")
        print(f"{'最小值':<10} {original_stats['min']:<12.2f} {distance_stats['min']:<12.2f} {(distance_stats['min']-original_stats['min'])/original_stats['min']*100:>8.1f}%")
        
        # 计算相关性
        correlation = pd.Series(original_scores).corr(pd.Series(distance_weighted_scores))
        print(f"\n评分相关性: {correlation:.3f}")
        
        return original_scores, distance_weighted_scores
    
    def _generate_comparison_report(self):
        """生成对比报告"""
        print("\n生成对比报告...")
        
        # 分析距离分布
        all_distances, distance_by_type = self.analyze_distance_distribution()
        
        # 计算评分对比
        original_scores, distance_scores = self._compare_scoring_effects()
        
        # 生成报告内容
        report = []
        report.append("# 基于距离权重的POI计算改进报告\n")
        
        report.append("## 1. 改进原理\n")
        report.append("### 核心思想")
        report.append("距离充电站越近的POI，对充电站的价值越大。使用指数衰减函数计算距离权重：")
        report.append(f"```")
        report.append(f"weight = exp(-distance/{self.decay_factor})")
        report.append(f"最大有效距离: {self.max_distance}米")
        report.append(f"最小权重: {self.min_weight}")
        report.append(f"```\n")
        
        report.append("### 权重示例")
        key_distances = [100, 200, 500, 1000, 2000, 3000]
        report.append("| 距离 | 权重 | 说明 |")
        report.append("|------|------|------|")
        for d in key_distances:
            w = self.calculate_distance_weight(d)
            if d <= 200:
                desc = "很近，高价值"
            elif d <= 500:
                desc = "较近，中高价值"
            elif d <= 1000:
                desc = "中等距离，中等价值"
            elif d <= 2000:
                desc = "较远，低价值"
            else:
                desc = "很远，极低价值"
            report.append(f"| {d}米 | {w:.3f} | {desc} |")
        report.append("")
        
        # 距离分布统计
        distances_array = np.array(all_distances)
        report.append("## 2. POI距离分布分析\n")
        report.append(f"- 总POI数量: {len(all_distances)}")
        report.append(f"- 平均距离: {distances_array.mean():.0f}米")
        report.append(f"- 中位数距离: {np.median(distances_array):.0f}米")
        report.append(f"- 距离范围: {distances_array.min():.0f}米 - {distances_array.max():.0f}米\n")
        
        # 评分对比统计
        original_stats = pd.Series(original_scores).describe()
        distance_stats = pd.Series(distance_scores).describe()
        correlation = pd.Series(original_scores).corr(pd.Series(distance_scores))
        
        report.append("## 3. 改进效果对比\n")
        report.append("| 指标 | 原有方法 | 距离权重方法 | 变化幅度 |")
        report.append("|------|----------|-------------|----------|")
        report.append(f"| 平均分 | {original_stats['mean']:.2f} | {distance_stats['mean']:.2f} | {(distance_stats['mean']-original_stats['mean'])/original_stats['mean']*100:+.1f}% |")
        report.append(f"| 标准差 | {original_stats['std']:.2f} | {distance_stats['std']:.2f} | {(distance_stats['std']-original_stats['std'])/original_stats['std']*100:+.1f}% |")
        report.append(f"| 评分相关性 | - | - | {correlation:.3f} |\n")
        
        report.append("## 4. 主要优势\n")
        report.append("1. **更符合实际**: 考虑了用户的实际行为模式")
        report.append("2. **精细化评分**: 同类POI根据距离有不同权重")
        report.append("3. **参数可调**: 可根据业务需求调整衰减参数")
        report.append("4. **保持兼容**: 不改变现有权重体系\n")
        
        report.append("## 5. 参数调优建议\n")
        report.append(f"当前参数设置:")
        report.append(f"- 衰减因子: {self.decay_factor}米")
        report.append(f"- 最大距离: {self.max_distance}米")
        report.append(f"- 最小权重: {self.min_weight}")
        report.append("")
        report.append("调优建议:")
        report.append("- 衰减因子越小，距离影响越大")
        report.append("- 最大距离应根据用户行为半径设定")
        report.append("- 最小权重避免远距离POI完全失去价值\n")
        
        # 保存报告
        os.makedirs('output', exist_ok=True)
        with open('output/distance_weight_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("距离权重对比报告已保存到 output/distance_weight_report.md")
    
    def save_distance_weighted_vectors(self, filepath='output/distance_weighted_poi_vectors.csv'):
        """保存距离权重POI向量"""
        if self.distance_weighted_vectors is not None:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            self.distance_weighted_vectors.to_csv(filepath)
            print(f"距离权重POI向量已保存到 {filepath}")

def main():
    """主函数，演示距离权重效果"""
    print("基于距离权重的POI计算演示")
    print("=" * 50)
    
    # 创建距离权重计算器
    calculator = DistanceWeightedPOICalculator(
        decay_factor=1000,  # 1000米衰减因子
        max_distance=3000,  # 3000米最大距离
        min_weight=0.1      # 0.1最小权重
    )
    
    # 加载数据
    calculator.load_data()
    
    # 对比两种方法
    calculator.compare_methods()
    
    # 保存结果
    calculator.save_distance_weighted_vectors()
    
    print("\n距离权重演示完成！")

if __name__ == "__main__":
    main()
