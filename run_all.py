#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
一键运行所有分析和整理过程
"""

import os
import subprocess
import time

def run_command(command, description):
    """运行命令并显示进度"""
    print(f"\n{'='*50}")
    print(f"正在{description}...")
    print(f"{'='*50}")
    
    start_time = time.time()
    process = subprocess.Popen(command, shell=True)
    process.wait()
    end_time = time.time()
    
    if process.returncode == 0:
        print(f"\n{description}完成！耗时 {end_time - start_time:.2f} 秒")
    else:
        print(f"\n{description}失败！错误代码: {process.returncode}")
    
    return process.returncode == 0

def main():
    """主函数"""
    # 确保 output 目录存在
    os.makedirs('output', exist_ok=True)
    
    # 运行场站分析
    if run_command("python3 station_analysis.py", "分析场站数据"):
        # 运行新场站预测
        if run_command("python3 predict_new_station.py", "预测新场站适合度"):
            # 复制文档到 output 文件夹
            run_command("python3 copy_docs_to_output.py", "复制文档到 output 文件夹")
    
    print("\n所有任务已完成！结果保存在 output 文件夹中")
    print("可以查看 output/使用说明.md 了解如何使用和查看结果")

if __name__ == "__main__":
    main()
