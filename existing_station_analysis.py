#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现有充电场站综合分析工具
分析现有107个充电场站的运营表现、POI环境和综合评分
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class ExistingStationAnalyzer:
    def __init__(self):
        self.stations_df = None
        self.comprehensive_df = None
        self.metrics_df = None
        self.scores_df = None
        
    def load_data(self):
        """加载所有相关数据文件"""
        print("正在加载数据文件...")
        
        # 1. 场站基础信息
        if os.path.exists('resource/final_stations_107.csv'):
            self.stations_df = pd.read_csv('resource/final_stations_107.csv')
            print(f"✓ 场站基础信息: {len(self.stations_df)} 个场站")
        else:
            print("✗ 场站基础信息文件不存在")
            
        # 2. 综合评分结果
        if os.path.exists('output/comprehensive_report.csv'):
            self.comprehensive_df = pd.read_csv('output/comprehensive_report.csv')
            print(f"✓ 综合评分数据: {len(self.comprehensive_df)} 个场站")
        else:
            print("✗ 综合评分文件不存在")
            
        # 3. 运营指标
        if os.path.exists('output/station_metrics.csv'):
            self.metrics_df = pd.read_csv('output/station_metrics.csv')
            print(f"✓ 运营指标数据: {len(self.metrics_df)} 个场站")
        else:
            print("✗ 运营指标文件不存在")
            
        # 4. 场站评分
        if os.path.exists('output/station_scores.csv'):
            self.scores_df = pd.read_csv('output/station_scores.csv')
            print(f"✓ 场站评分数据: {len(self.scores_df)} 个场站")
        else:
            print("✗ 场站评分文件不存在")
    
    def generate_overview_report(self):
        """生成概览报告"""
        print("\n" + "="*60)
        print("现有充电场站综合分析报告")
        print("="*60)
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 基础统计
        print(f"\n【数据概览】")
        if self.stations_df is not None:
            print(f"总场站数量: {len(self.stations_df)}")
        if self.comprehensive_df is not None:
            print(f"有综合评分的场站: {len(self.comprehensive_df)}")
        if self.metrics_df is not None:
            print(f"有运营数据的场站: {len(self.metrics_df)}")
            
        # 运营数据统计
        if self.metrics_df is not None:
            self.analyze_operational_performance()
            
        # 综合评分分析
        if self.comprehensive_df is not None:
            self.analyze_comprehensive_scores()
            
        # 场站分类分析
        self.analyze_station_categories()
        
    def analyze_operational_performance(self):
        """分析运营表现"""
        print(f"\n【运营表现分析】")
        
        df = self.metrics_df
        
        # 总体统计
        total_orders = df['total_orders'].sum()
        total_revenue = df['total_revenue'].sum()
        avg_utilization = df['equipment_utilization'].mean()
        avg_daily_revenue = df['avg_daily_revenue'].mean()
        
        print(f"总订单量: {total_orders:,.0f}")
        print(f"总收益: ¥{total_revenue:,.2f}")
        print(f"平均设备利用率: {avg_utilization:.2%}")
        print(f"平均日收益: ¥{avg_daily_revenue:.2f}")
        
        # 收益前10名
        print(f"\n【收益排行榜 TOP 10】")
        top_revenue = df.nlargest(10, 'total_revenue')
        for i, (_, row) in enumerate(top_revenue.iterrows(), 1):
            revenue = row['total_revenue']
            orders = row['total_orders']
            utilization = row['equipment_utilization']
            equipment_count = row.get('equipment_count', 0)
            print(f"{i:2d}. {row['station_name'][:30]:30s} "
                  f"收益: ¥{revenue:8,.0f} "
                  f"订单: {orders:5.0f} "
                  f"设备: {equipment_count:2.0f} "
                  f"利用率: {utilization:6.2%}")
        
        # 利用率前10名
        print(f"\n【设备利用率排行榜 TOP 10】")
        top_utilization = df.nlargest(10, 'equipment_utilization')
        for i, (_, row) in enumerate(top_utilization.iterrows(), 1):
            revenue = row['total_revenue']
            utilization = row['equipment_utilization']
            equipment_count = row.get('equipment_count', 0)
            print(f"{i:2d}. {row['station_name'][:30]:30s} "
                  f"利用率: {utilization:6.2%} "
                  f"设备: {equipment_count:2.0f} "
                  f"收益: ¥{revenue:8,.0f}")
                  
    def analyze_comprehensive_scores(self):
        """分析综合评分"""
        print(f"\n【综合评分分析】")
        
        df = self.comprehensive_df
        
        # 综合评分前10名
        print(f"\n【综合评分排行榜 TOP 10】")
        top_combined = df.nlargest(10, 'combined_score')
        for i, (_, row) in enumerate(top_combined.iterrows(), 1):
            poi_score = row.get('poi_score', 0)
            perf_score = row.get('performance_score', 0)
            combined_score = row.get('combined_score', 0)
            print(f"{i:2d}. {row['station_name'][:30]:30s} "
                  f"综合: {combined_score:6.2f} "
                  f"(POI: {poi_score:5.2f}, 运营: {perf_score:5.2f})")
        
        # POI评分前10名
        if 'poi_score' in df.columns:
            print(f"\n【POI环境评分排行榜 TOP 10】")
            top_poi = df.nlargest(10, 'poi_score')
            for i, (_, row) in enumerate(top_poi.iterrows(), 1):
                poi_score = row['poi_score']
                combined_score = row.get('combined_score', 0)
                print(f"{i:2d}. {row['station_name'][:30]:30s} "
                      f"POI: {poi_score:6.2f} "
                      f"综合: {combined_score:6.2f}")
        
        # 运营表现前10名
        if 'performance_score' in df.columns:
            print(f"\n【运营表现评分排行榜 TOP 10】")
            top_perf = df.nlargest(10, 'performance_score')
            for i, (_, row) in enumerate(top_perf.iterrows(), 1):
                perf_score = row['performance_score']
                combined_score = row.get('combined_score', 0)
                print(f"{i:2d}. {row['station_name'][:30]:30s} "
                      f"运营: {perf_score:6.2f} "
                      f"综合: {combined_score:6.2f}")
                      
    def analyze_station_categories(self):
        """分析场站分类"""
        print(f"\n【场站分类分析】")
        
        if self.comprehensive_df is not None:
            df = self.comprehensive_df
            
            # 按运营商分类
            print(f"\n按运营商分类:")
            station_operators = {}
            for station_name in df['station_name']:
                if '易佳电' in station_name:
                    operator = '易佳电'
                elif '星桩互联' in station_name:
                    operator = '星桩互联'
                elif '深圳呼电' in station_name:
                    operator = '深圳呼电'
                elif '合肥市公交站' in station_name:
                    operator = '合肥公交'
                else:
                    operator = '其他'
                
                if operator not in station_operators:
                    station_operators[operator] = 0
                station_operators[operator] += 1
            
            for operator, count in sorted(station_operators.items(), key=lambda x: x[1], reverse=True):
                print(f"  {operator}: {count} 个场站")
                
            # 按评分等级分类
            if 'combined_score' in df.columns:
                print(f"\n按综合评分等级分类:")
                excellent = len(df[df['combined_score'] >= 20])
                good = len(df[(df['combined_score'] >= 10) & (df['combined_score'] < 20)])
                average = len(df[(df['combined_score'] >= 5) & (df['combined_score'] < 10)])
                poor = len(df[df['combined_score'] < 5])
                
                print(f"  优秀 (≥20分): {excellent} 个场站")
                print(f"  良好 (10-20分): {good} 个场站")
                print(f"  一般 (5-10分): {average} 个场站")
                print(f"  较差 (<5分): {poor} 个场站")
                
    def identify_improvement_opportunities(self):
        """识别改进机会"""
        print(f"\n【改进机会识别】")
        
        if self.comprehensive_df is not None:
            df = self.comprehensive_df
            
            # 低效场站识别
            print(f"\n需要重点关注的场站 (综合评分 < 5分):")
            low_performers = df[df['combined_score'] < 5].sort_values('combined_score')
            for i, (_, row) in enumerate(low_performers.head(10).iterrows(), 1):
                combined_score = row['combined_score']
                poi_score = row.get('poi_score', 0)
                perf_score = row.get('performance_score', 0)
                print(f"{i:2d}. {row['station_name'][:35]:35s} "
                      f"综合: {combined_score:5.2f} "
                      f"(POI: {poi_score:5.2f}, 运营: {perf_score:5.2f})")
            
            # POI环境好但运营差的场站
            if 'poi_score' in df.columns and 'performance_score' in df.columns:
                print(f"\nPOI环境好但运营表现差的场站 (POI>2, 运营<10):")
                potential_improvers = df[(df['poi_score'] > 2) & (df['performance_score'] < 10)]
                potential_improvers = potential_improvers.sort_values('poi_score', ascending=False)
                for i, (_, row) in enumerate(potential_improvers.head(5).iterrows(), 1):
                    poi_score = row['poi_score']
                    perf_score = row['performance_score']
                    print(f"{i:2d}. {row['station_name'][:35]:35s} "
                          f"POI: {poi_score:5.2f} "
                          f"运营: {perf_score:5.2f}")
                          
    def generate_summary_insights(self):
        """生成总结洞察"""
        print(f"\n【关键洞察】")
        
        insights = []
        
        if self.comprehensive_df is not None:
            df = self.comprehensive_df
            
            # 最佳表现场站
            best_station = df.loc[df['combined_score'].idxmax()]
            insights.append(f"• 综合表现最佳场站: {best_station['station_name']} (评分: {best_station['combined_score']:.2f})")
            
            # 评分分布
            avg_score = df['combined_score'].mean()
            insights.append(f"• 平均综合评分: {avg_score:.2f}")
            
            # 运营商表现
            operator_scores = {}
            for _, row in df.iterrows():
                station_name = row['station_name']
                score = row['combined_score']
                
                if '易佳电' in station_name:
                    operator = '易佳电'
                elif '星桩互联' in station_name:
                    operator = '星桩互联'
                elif '深圳呼电' in station_name:
                    operator = '深圳呼电'
                else:
                    operator = '其他'
                
                if operator not in operator_scores:
                    operator_scores[operator] = []
                operator_scores[operator].append(score)
            
            for operator, scores in operator_scores.items():
                if len(scores) >= 3:  # 至少3个场站才统计
                    avg_score = np.mean(scores)
                    insights.append(f"• {operator}平均评分: {avg_score:.2f} ({len(scores)}个场站)")
        
        for insight in insights:
            print(insight)
            
    def run_complete_analysis(self):
        """运行完整分析"""
        self.load_data()
        self.generate_overview_report()
        self.identify_improvement_opportunities()
        self.generate_summary_insights()
        
        print(f"\n" + "="*60)
        print("分析完成！")
        print("="*60)

if __name__ == "__main__":
    analyzer = ExistingStationAnalyzer()
    analyzer.run_complete_analysis()
