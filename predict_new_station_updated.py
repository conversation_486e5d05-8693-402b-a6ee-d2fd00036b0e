#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
新建场站决策辅助功能 - 新场站预测示例

此脚本演示如何使用StationAnalyzer类预测新场站的适合度，
并结合订单数据进行更准确的预测。
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
from station_analysis import StationAnalyzer
import seaborn as sns

def main():
    """主函数"""
    print("新建场站决策辅助系统 - 新场站预测示例")
    print("=" * 50)

    # 初始化分析器（包含订单数据）
    analyzer = StationAnalyzer(order_data_file='resource/2025_Q2_10cols_172stations.csv')

    # 加载数据
    analyzer.load_data()

    # 构建POI向量
    analyzer.build_poi_vectors()

    # 计算相似度
    analyzer.calculate_similarity()

    # 构建评分框架（会自动使用订单数据优化权重）
    analyzer.build_scoring_framework()

    print("\n请输入新场站的POI数据文件路径（JSON格式）：")
    print("示例：raw_poi_data/某新场站_经度_纬度_日期_时间.json")

    # 在实际应用中，这里应该接受用户输入
    # 为了演示，我们使用一个现有的场站数据作为"新"场站
    sample_station_file = list(analyzer.raw_poi_data.keys())[0]
    sample_station_data = analyzer.raw_poi_data[sample_station_file]['data']

    print(f"使用 {sample_station_file} 的数据作为示例...")

    # 预测新场站的适合度
    similar_stations = analyzer.predict_new_station(sample_station_data)

    print("\n与新场站最相似的现有场站：")
    for station, similarity in similar_stations.items():
        print(f"{station}: 相似度 {similarity:.4f}")

    # 获取相似场站的得分
    similar_station_scores = []
    similar_station_performance = []
    
    # 检查是否有业绩评分
    has_performance_scores = hasattr(analyzer, 'performance_scores') and analyzer.performance_scores is not None
    
    for station in similar_stations.index:
        # 获取POI评分
        poi_score = analyzer.evaluate_station_score(station)
        similar_station_scores.append((station, poi_score))
        
        # 获取业绩评分（如果有）
        if has_performance_scores and station in analyzer.performance_scores:
            performance_score = analyzer.performance_scores[station]
            similar_station_performance.append((station, performance_score))

    # 按得分排序
    similar_station_scores.sort(key=lambda x: x[1], reverse=True)

    print("\n相似场站的POI评分：")
    for station, score in similar_station_scores:
        print(f"{station}: 得分 {score:.2f}")
        
    # 如果有业绩评分，显示业绩评分
    if similar_station_performance:
        similar_station_performance.sort(key=lambda x: x[1], reverse=True)
        print("\n相似场站的业绩评分：")
        for station, score in similar_station_performance:
            print(f"{station}: 得分 {score:.2f}")

    # 计算平均得分作为新场站的预测得分
    avg_poi_score = sum([score for _, score in similar_station_scores]) / len(similar_station_scores)
    print(f"\n新场站的预测 POI 得分: {avg_poi_score:.2f}")
    
    # 如果有订单数据，预测业绩
    combined_score = avg_poi_score
    avg_performance_score = None
    predicted_revenue = None
    
    if analyzer.order_analyzer is not None:
        # 处理新场站的POI向量
        new_vector = pd.DataFrame(0, index=['new_station'], columns=analyzer.poi_vectors.columns)
        for poi in sample_station_data.get('pois', []):
            if 'typecode' in poi:
                typecode = poi['typecode']
                # 获取大类和中类编码
                if len(typecode) == 6:  # 确保是6位编码
                    large_category = typecode[:2] + '0000'  # 大类
                    medium_category = typecode[:4] + '00'   # 中类
                    
                    # 先检查具体编码是否在有效编码中
                    if typecode in analyzer.used_poi_codes and typecode in new_vector.columns:
                        new_vector.at['new_station', typecode] += 1
                    
                    # 然后检查中类编码是否在有效编码中
                    if medium_category in analyzer.used_poi_codes and medium_category in new_vector.columns:
                        new_vector.at['new_station', medium_category] += 1
                    
                    # 最后检查大类编码是否在有效编码中
                    if large_category in analyzer.used_poi_codes and large_category in new_vector.columns:
                        new_vector.at['new_station', large_category] += 1
        
        # 预测新场站的营业额
        if hasattr(analyzer.order_analyzer, 'performance_model') and analyzer.order_analyzer.performance_model is not None:
            predicted_revenue = analyzer.order_analyzer.predict_new_station_performance(new_vector.iloc[0], analyzer.poi_vectors)
            if predicted_revenue is not None:
                print(f"预测的新场站周营业额: {predicted_revenue:.2f}")
                
                # 如果有业绩评分，计算综合评分
                if similar_station_performance:
                    avg_performance_score = sum([score for _, score in similar_station_performance]) / len(similar_station_performance)
                    print(f"新场站的预测业绩得分: {avg_performance_score:.2f}")
                    
                    # 计算综合评分
                    combined_score = 0.6 * avg_poi_score + 0.4 * avg_performance_score
                    print(f"新场站的预测综合得分: {combined_score:.2f}")
    
    # 评估新场站的适合度
    if combined_score >= 70:
        recommendation = "非常适合"
    elif combined_score >= 60:
        recommendation = "适合"
    elif combined_score >= 50:
        recommendation = "一般"
    else:
        recommendation = "不太适合"

    print(f"建议: {recommendation}建设新场站")

    # 可视化相似场站的得分
    plt.figure(figsize=(12, 8))
    
    # 创建数据
    stations = [station for station, _ in similar_station_scores]
    poi_scores = [score for _, score in similar_station_scores]
    
    # 如果有业绩评分，创建多维度柱状图
    if similar_station_performance:
        # 创建业绩评分数据
        performance_dict = dict(similar_station_performance)
        performance_scores = [performance_dict.get(station, 0) for station in stations]
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Station': stations,
            'POI Score': poi_scores,
            'Performance Score': performance_scores
        })
        
        # 计算综合评分
        df['Combined Score'] = 0.6 * df['POI Score'] + 0.4 * df['Performance Score']
        
        # 使用Seaborn创建多维度柱状图
        plt.figure(figsize=(15, 10))
        df_melted = pd.melt(df, id_vars=['Station'], value_vars=['POI Score', 'Performance Score', 'Combined Score'],
                           var_name='Score Type', value_name='Score')
        
        sns.barplot(x='Station', y='Score', hue='Score Type', data=df_melted)
        plt.title('相似场站的多维度评分对比')
        plt.xticks(rotation=45, ha='right')
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
    else:
        # 创建简单柱状图
        bars = plt.bar(range(len(stations)), poi_scores, color='skyblue')
        
        # 设置x轴刻度和标签
        plt.xticks(range(len(stations)), stations, rotation=45, ha='right')
        
        # 在柱状图上方显示得分
        for i, bar in enumerate(bars):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.2,
                    f'{poi_scores[i]:.2f}',
                    ha='center', va='bottom')
        
        plt.title('相似场站的评分')
        plt.xlabel('场站')
        plt.ylabel('评分')
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()

    # 确保output目录存在
    os.makedirs('output', exist_ok=True)
    plt.savefig('output/new_station_prediction.png', dpi=300)
    
    # 如果有业绩数据，创建雷达图
    if similar_station_performance and analyzer.order_analyzer is not None and hasattr(analyzer.order_analyzer, 'station_metrics'):
        # 获取最相似的场站
        most_similar_station = similar_stations.index[0]
        
        # 获取该场站的业绩指标
        metrics = analyzer.order_analyzer.station_metrics
        if most_similar_station in metrics.index:
            # 选择要显示的指标
            selected_metrics = [
                'total_revenue', 'equipment_utilization', 'avg_daily_orders',
                'weekend_ratio', 'avg_order_revenue'
            ]
            
            # 指标的中文名称
            metric_names = {
                'total_revenue': '总营业额',
                'equipment_utilization': '设备利用率',
                'avg_daily_orders': '日均订单数',
                'weekend_ratio': '周末/工作日比',
                'avg_order_revenue': '平均订单金额'
            }
            
            # 获取指标值
            values = metrics.loc[most_similar_station, selected_metrics].values
            
            # 标准化指标值到0-1范围
            min_values = metrics[selected_metrics].min()
            max_values = metrics[selected_metrics].max()
            normalized_values = (values - min_values) / (max_values - min_values)
            
            # 创建雷达图
            plt.figure(figsize=(10, 8))
            
            # 计算角度
            angles = np.linspace(0, 2*np.pi, len(selected_metrics), endpoint=False).tolist()
            angles += angles[:1]  # 闭合图形
            
            # 添加值
            normalized_values = normalized_values.tolist()
            normalized_values += normalized_values[:1]  # 闭合图形
            
            # 绘制雷达图
            ax = plt.subplot(111, polar=True)
            ax.plot(angles, normalized_values, 'o-', linewidth=2, label=most_similar_station)
            ax.fill(angles, normalized_values, alpha=0.25)
            
            # 添加标签
            ax.set_thetagrids(np.degrees(angles[:-1]), [metric_names.get(m, m) for m in selected_metrics])
            
            # 设置y轴范围
            ax.set_ylim(0, 1)
            
            # 添加标题
            plt.title(f'最相似场站 {most_similar_station} 的业绩雷达图')
            
            plt.tight_layout()
            plt.savefig('output/new_station_radar.png', dpi=300)
    
    print("\n可视化结果已保存：")
    print("- output/new_station_prediction.png：相似场站得分对比图")
    print("- output/new_station_radar.png：新场站与最相似场站的多维度对比图")

if __name__ == "__main__":
    main()
