#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POI计算方法对比演示
对比原有计算方式与新的增强计算方式的效果
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import time
from station_analysis import StationAnalyzer
from enhanced_poi_calculator import EnhancedPOICalculator

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def run_comparison_demo():
    """运行POI计算方法对比演示"""
    print("POI计算方法对比演示")
    print("=" * 60)
    
    # 1. 初始化原有分析器
    print("\n1. 初始化原有分析器...")
    original_analyzer = StationAnalyzer()
    original_analyzer.load_data()
    
    # 2. 初始化增强计算器
    print("\n2. 初始化增强计算器...")
    enhanced_calculator = EnhancedPOICalculator()
    
    # 3. 使用原有方法构建POI向量
    print("\n3. 使用原有方法构建POI向量...")
    start_time = time.time()
    original_vectors = original_analyzer.build_poi_vectors()
    original_time = time.time() - start_time
    print(f"原有方法耗时: {original_time:.2f}秒")
    print(f"原有方法特征数: {original_vectors.shape[1]}")
    
    # 4. 使用增强方法构建POI向量
    print("\n4. 使用增强方法构建POI向量...")
    start_time = time.time()
    enhanced_vectors = enhanced_calculator.build_enhanced_poi_vectors(
        original_analyzer.raw_poi_data, 
        original_analyzer.used_poi_codes
    )
    enhanced_time = time.time() - start_time
    print(f"增强方法耗时: {enhanced_time:.2f}秒")
    print(f"增强方法特征数: {enhanced_vectors.shape[1]}")
    
    # 5. 计算原有方法的评分
    print("\n5. 计算原有方法的评分...")
    original_scores = []
    for station in original_vectors.index:
        score = original_analyzer.evaluate_station_score(station)
        original_scores.append(score)
    
    # 6. 计算增强方法的评分
    print("\n6. 计算增强方法的评分...")
    enhanced_scores = []
    for station in enhanced_vectors.index:
        if station in original_vectors.index:
            score = enhanced_calculator.calculate_station_score(
                enhanced_vectors.loc[station]
            )
            enhanced_scores.append(score)
        else:
            enhanced_scores.append(0)
    
    # 7. 如果有订单数据，进行权重优化
    print("\n7. 尝试进行权重优化...")
    if original_analyzer.order_analyzer is not None:
        try:
            # 准备业绩数据
            original_analyzer.order_analyzer.calculate_station_metrics()
            performance_data = original_analyzer.order_analyzer.station_metrics[['total_revenue']].copy()
            performance_data.columns = ['revenue']
            
            # 使用增强计算器优化权重
            optimized_weights = enhanced_calculator.optimize_weights_with_performance(performance_data)
            
            if optimized_weights:
                print("权重优化成功！")
                # 重新计算优化后的评分
                optimized_scores = []
                for station in enhanced_vectors.index:
                    if station in original_vectors.index:
                        score = enhanced_calculator.calculate_station_score(
                            enhanced_vectors.loc[station], optimized_weights
                        )
                        optimized_scores.append(score)
                    else:
                        optimized_scores.append(0)
            else:
                optimized_scores = enhanced_scores.copy()
                print("权重优化失败，使用基础权重")
        except Exception as e:
            print(f"权重优化过程中出错: {e}")
            optimized_scores = enhanced_scores.copy()
    else:
        optimized_scores = enhanced_scores.copy()
        print("无订单数据，跳过权重优化")
    
    # 8. 生成对比分析
    print("\n8. 生成对比分析...")
    generate_comparison_analysis(
        original_vectors, enhanced_vectors,
        original_scores, enhanced_scores, optimized_scores,
        original_time, enhanced_time
    )
    
    # 9. 生成可视化对比
    print("\n9. 生成可视化对比...")
    create_comparison_visualizations(
        original_scores, enhanced_scores, optimized_scores,
        original_vectors, enhanced_vectors
    )
    
    print("\n对比演示完成！结果已保存到output目录")

def generate_comparison_analysis(original_vectors, enhanced_vectors,
                               original_scores, enhanced_scores, optimized_scores,
                               original_time, enhanced_time):
    """生成对比分析报告"""
    
    # 计算统计指标
    orig_stats = pd.Series(original_scores).describe()
    enh_stats = pd.Series(enhanced_scores).describe()
    opt_stats = pd.Series(optimized_scores).describe()
    
    # 计算相关性
    correlation_orig_enh = pd.Series(original_scores).corr(pd.Series(enhanced_scores))
    correlation_orig_opt = pd.Series(original_scores).corr(pd.Series(optimized_scores))
    
    # 生成报告
    report = []
    report.append("# POI计算方法对比分析报告\n")
    
    report.append("## 1. 基本信息对比\n")
    report.append("| 方法 | 特征数量 | 计算时间(秒) | 平均分 | 标准差 | 最小值 | 最大值 |")
    report.append("|------|----------|-------------|--------|--------|--------|--------|")
    report.append(f"| 原有方法 | {original_vectors.shape[1]} | {original_time:.2f} | {orig_stats['mean']:.2f} | {orig_stats['std']:.2f} | {orig_stats['min']:.2f} | {orig_stats['max']:.2f} |")
    report.append(f"| 增强方法 | {enhanced_vectors.shape[1]} | {enhanced_time:.2f} | {enh_stats['mean']:.2f} | {enh_stats['std']:.2f} | {enh_stats['min']:.2f} | {enh_stats['max']:.2f} |")
    report.append(f"| 优化方法 | {enhanced_vectors.shape[1]} | - | {opt_stats['mean']:.2f} | {opt_stats['std']:.2f} | {opt_stats['min']:.2f} | {opt_stats['max']:.2f} |\n")
    
    report.append("## 2. 改进效果分析\n")
    
    # 计算改进幅度
    feature_improvement = (enhanced_vectors.shape[1] - original_vectors.shape[1]) / original_vectors.shape[1] * 100
    std_improvement = (enh_stats['std'] - orig_stats['std']) / orig_stats['std'] * 100
    
    report.append(f"- **特征数量增加**: {feature_improvement:.1f}%")
    report.append(f"- **评分区分度变化**: {std_improvement:.1f}%")
    report.append(f"- **原有方法与增强方法相关性**: {correlation_orig_enh:.3f}")
    report.append(f"- **原有方法与优化方法相关性**: {correlation_orig_opt:.3f}\n")
    
    report.append("## 3. 主要改进点\n")
    report.append("### 3.1 距离权重")
    report.append("- 考虑POI到充电站的距离，距离越近影响越大")
    report.append("- 使用指数衰减函数计算距离权重\n")
    
    report.append("### 3.2 POI质量评估")
    report.append("- 根据POI名称长度判断重要性")
    report.append("- 根据POI类型详细程度判断重要性")
    report.append("- 根据地址信息判断位置重要性\n")
    
    report.append("### 3.3 密度特征")
    report.append("- 增加不同半径内的POI密度特征")
    report.append("- 计算POI多样性指数")
    report.append("- 分类别计算POI密度\n")
    
    report.append("### 3.4 避免重复计算")
    report.append("- 优化层次结构计算逻辑")
    report.append("- 优先使用最具体的POI编码\n")
    
    report.append("### 3.5 机器学习权重优化")
    report.append("- 基于实际业绩数据训练模型")
    report.append("- 自动优化POI权重")
    report.append("- 提供特征重要性分析\n")
    
    # 保存报告
    os.makedirs('output', exist_ok=True)
    with open('output/poi_comparison_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("对比分析报告已保存到 output/poi_comparison_report.md")

def create_comparison_visualizations(original_scores, enhanced_scores, optimized_scores,
                                   original_vectors, enhanced_vectors):
    """创建可视化对比图"""
    
    # 1. 评分分布对比
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.hist(original_scores, bins=20, alpha=0.7, color='blue', edgecolor='black')
    plt.title('原有方法评分分布')
    plt.xlabel('评分')
    plt.ylabel('站点数量')
    plt.grid(axis='y', alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.hist(enhanced_scores, bins=20, alpha=0.7, color='green', edgecolor='black')
    plt.title('增强方法评分分布')
    plt.xlabel('评分')
    plt.ylabel('站点数量')
    plt.grid(axis='y', alpha=0.3)
    
    plt.subplot(1, 3, 3)
    plt.hist(optimized_scores, bins=20, alpha=0.7, color='red', edgecolor='black')
    plt.title('优化方法评分分布')
    plt.xlabel('评分')
    plt.ylabel('站点数量')
    plt.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('output/score_distribution_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 评分相关性散点图
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(original_scores, enhanced_scores, alpha=0.6, color='green')
    plt.plot([min(original_scores), max(original_scores)], 
             [min(original_scores), max(original_scores)], 'r--', alpha=0.8)
    plt.xlabel('原有方法评分')
    plt.ylabel('增强方法评分')
    plt.title('原有方法 vs 增强方法')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.scatter(original_scores, optimized_scores, alpha=0.6, color='red')
    plt.plot([min(original_scores), max(original_scores)], 
             [min(original_scores), max(original_scores)], 'r--', alpha=0.8)
    plt.xlabel('原有方法评分')
    plt.ylabel('优化方法评分')
    plt.title('原有方法 vs 优化方法')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('output/score_correlation_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 特征数量对比
    plt.figure(figsize=(10, 6))
    methods = ['原有方法', '增强方法']
    feature_counts = [original_vectors.shape[1], enhanced_vectors.shape[1]]
    colors = ['blue', 'green']
    
    bars = plt.bar(methods, feature_counts, color=colors, alpha=0.7)
    plt.ylabel('特征数量')
    plt.title('特征数量对比')
    plt.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bar, count in zip(bars, feature_counts):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('output/feature_count_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("可视化对比图已保存到output目录")

if __name__ == "__main__":
    run_comparison_demo()
