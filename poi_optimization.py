#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POI数据获取与类型选择优化工具
"""

import os
import json
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
from sklearn.ensemble import RandomForestRegressor
import requests

class POIOptimizer:
    """POI数据获取与类型选择优化器"""

    def __init__(self):
        """初始化优化器"""
        # 高德地图API密钥
        self.api_key = "9bc017d3ba3c32eb6fb2d9fe0d224d96"

        # 初始化数据
        self.poi_categories = None  # POI分类编码
        self.raw_poi_data = {}  # 原始POI数据
        self.station_revenues_q1 = {}  # 第一季度场站营业额数据
        self.station_revenues_q2 = {}  # 第二季度场站营业额数据
        self.relevance_scores = {}  # POI类型与充电站的相关性评分
        self.selected_poi_types = []  # 选择的POI类型

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")

        # 加载POI分类编码
        try:
            self.poi_categories = pd.read_excel('resource/高德POI分类与编码（中英文）_V1.06_20230208.xlsx')
            self.poi_categories['NEW_TYPE'] = self.poi_categories['NEW_TYPE'].astype(str)
            print(f"已加载POI分类编码，共 {len(self.poi_categories)} 条记录")
        except Exception as e:
            print(f"加载POI分类编码时出错: {e}")
            return False

        # 加载原始POI数据
        self._load_raw_poi_data()

        # 加载场站营业额数据（第一季度）
        self._load_station_revenues_q1()

        # 加载场站营业额数据（第二季度）
        self._load_station_revenues_q2()

        return True

    def _load_raw_poi_data(self):
        """加载原始POI数据"""
        raw_poi_dir = 'poi_data'
        count = 0

        if os.path.exists(raw_poi_dir):
            for filename in os.listdir(raw_poi_dir):
                if filename.endswith('.json'):
                    station_name = filename.replace('.json', '')

                    try:
                        parts = station_name.split('_')
                        longitude = float(parts[1])
                        latitude = float(parts[2])

                        with open(os.path.join(raw_poi_dir, filename), 'r', encoding='utf-8') as f:
                            data = json.load(f)

                            # # 提取经纬度信息
                            # # 由于文件已重命名，我们需要从数据中提取经纬度
                            # if 'data' in data and 'pois' in data['data'] and len(data['data']['pois']) > 0:
                            #     # 使用第一个POI的位置作为场站位置的近似值
                            #     first_poi = data['data']['pois'][0]
                            #     if 'location' in first_poi:
                            #         location = first_poi['location'].split(',')
                            #         longitude, latitude = float(location[0]), float(location[1])

                            self.raw_poi_data[station_name] = {
                                'data': data['data'],
                                'longitude': longitude,
                                'latitude': latitude
                            }
                            count += 1
                    except Exception as e:
                        print(f"加载POI数据文件 {filename} 时出错: {e}")

        print(f"已加载 {count} 个场站的原始POI数据")

    def _load_station_revenues_q1(self):
        """加载第一季度场站营业额数据"""
        try:
            # 尝试加载订单数据
            order_data = pd.read_csv('resource/output_2025_Q1_10cols_107stations.csv')

            # 计算每个场站的总营业额
            station_revenues = order_data.groupby('station_name')['total_fees'].sum()

            # 转换为字典
            self.station_revenues_q1 = station_revenues.to_dict()

            print(f"已加载 {len(self.station_revenues_q1)} 个场站的第一季度营业额数据")
        except Exception as e:
            print(f"加载第一季度场站营业额数据时出错: {e}")
            self.station_revenues_q1 = {}

    def _load_station_revenues_q2(self):
        """加载第二季度场站营业额数据"""
        try:
            # 尝试加载订单数据
            order_data = pd.read_csv('resource/2025_Q2_10cols_107stations.csv')

            # 计算每个场站的总营业额
            station_revenues = order_data.groupby('station_name')['total_fees'].sum()

            # 转换为字典
            self.station_revenues_q2 = station_revenues.to_dict()

            print(f"已加载 {len(self.station_revenues_q2)} 个场站的第二季度营业额数据")
        except Exception as e:
            print(f"加载第二季度场站营业额数据时出错: {e}")
            self.station_revenues_q2 = {}

    def analyze_poi_coverage(self):
        """分析当前POI数据的覆盖情况"""
        print("\n正在分析当前POI数据的覆盖情况...")

        if not self.raw_poi_data:
            print("没有POI数据，无法分析覆盖情况")
            return False

        # 统计所有POI类型的出现频率
        poi_counts = defaultdict(int)
        for _, station_info in self.raw_poi_data.items():
            for poi in station_info['data'].get('pois', []):
                if 'typecode' in poi:
                    poi_counts[poi['typecode']] += 1

        # 统计大类的覆盖情况
        large_categories = set()
        covered_large_categories = set()

        for _, row in self.poi_categories.iterrows():
            typecode = row['NEW_TYPE']
            if len(typecode) >= 2:
                large_category = typecode[:2]
                large_categories.add(large_category)

                if any(code.startswith(large_category) for code in poi_counts.keys()):
                    covered_large_categories.add(large_category)

        # 获取当前使用的POI类型
        current_poi_types = []
        try:
            with open('resource/weightPOI', 'r', encoding='utf-8') as f:
                current_poi_types = [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"读取当前使用的POI类型时出错: {e}")

        # 打印覆盖情况
        print("\n当前POI数据覆盖情况：")
        print(f"总POI类型数: {len(self.poi_categories)}")
        print(f"当前数据中的POI类型数: {len(poi_counts)}")
        print(f"大类总数: {len(large_categories)}")
        print(f"已覆盖大类数: {len(covered_large_categories)}")
        print(f"缺失大类数: {len(large_categories) - len(covered_large_categories)}")
        print(f"当前使用的POI类型数: {len(current_poi_types)}")

        # 打印缺失的大类
        missing_large_categories = large_categories - covered_large_categories
        if missing_large_categories:
            print("\n缺失的大类：")
            for category in sorted(missing_large_categories):
                # 查找大类名称
                matches = self.poi_categories[self.poi_categories['NEW_TYPE'].str.startswith(category)]
                if len(matches) > 0:
                    category_name = matches.iloc[0]['大类']
                    print(f"{category}: {category_name}")
                else:
                    print(f"{category}: 未知")

        # 打印最常见的POI类型
        print("\n最常见的POI类型（前20个）：")
        print(f"{'编码':<10}{'出现次数':<10}{'大类':<15}{'中类':<20}{'小类':<25}")
        print("-" * 80)

        for typecode, count in sorted(poi_counts.items(), key=lambda x: x[1], reverse=True)[:20]:
            # 查找POI类型的名称
            matches = self.poi_categories[self.poi_categories['NEW_TYPE'] == typecode]
            if len(matches) > 0:
                row = matches.iloc[0]
                print(f"{typecode:<10}{count:<10}  {row['大类']:<15}{row['中类']:<20}{row['小类']:<25}")
            else:
                print(f"{typecode:<10}{count:<10}  {'未找到':<15}{'未找到':<20}{'未找到':<25}")

        return True

    def optimize_poi_type_selection(self):
        """基于两个季度的数据优化POI类型选择"""
        print("\n正在基于两个季度的数据优化POI类型选择...")

        if not self.raw_poi_data:
            print("没有POI数据，无法优化POI类型选择")
            return False

        if not self.station_revenues_q1 or not self.station_revenues_q2:
            print("缺少场站营业额数据，无法优化POI类型选择")
            return False

        # 统计所有POI类型的出现频率
        poi_counts = defaultdict(int)
        for _, station_info in self.raw_poi_data.items():
            for poi in station_info['data'].get('pois', []):
                if 'typecode' in poi:
                    poi_counts[poi['typecode']] += 1

        print(f"在POI数据中找到 {len(poi_counts)} 个不同的POI类型")

        # 找到两个季度都有数据的场站
        common_stations = [station for station in self.raw_poi_data.keys()
                          if station in self.station_revenues_q1
                          and station in self.station_revenues_q2]

        all_typecodes = [typecode for typecode, count in poi_counts.items() if count >= 5]  # 只使用出现至少5次的POI类型

        print(f"使用 {len(common_stations)} 个有两季度数据的场站和 {len(all_typecodes)} 个常见POI类型进行分析")

        if len(common_stations) < 10 or len(all_typecodes) < 10:
            print("数据不足，无法进行可靠的分析")
            return False

        # 构建增强的特征矩阵
        X = np.zeros((len(common_stations), len(all_typecodes) * 2 + 1))
        y = np.array([self.station_revenues_q2[station] for station in common_stations])

        for i, station in enumerate(common_stations):
            # 当前POI数据
            for j, typecode in enumerate(all_typecodes):
                X[i, j] = sum(1 for poi in self.raw_poi_data[station]['data'].get('pois', [])
                             if 'typecode' in poi and poi['typecode'] == typecode)

            # 添加第一季度的营业额作为特征
            X[i, -1] = self.station_revenues_q1[station]

            # 添加季度增长率特征
            growth_rate = (self.station_revenues_q2[station] - self.station_revenues_q1[station]) / max(1, self.station_revenues_q1[station])
            X[i, -2] = growth_rate

        # 使用随机森林进行特征选择
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y)

        # 获取特征重要性
        importances = rf.feature_importances_
        indices = np.argsort(importances)[::-1]

        # 选择重要性高的特征
        self.selected_poi_types = []
        for i in indices:
            if i < len(all_typecodes):  # 只选择POI类型特征，不包括营业额和增长率特征
                typecode = all_typecodes[i]
                self.selected_poi_types.append((typecode, importances[i]))

            if len(self.selected_poi_types) >= 50:  # 选择前50个重要特征
                break

        # 打印选择的POI类型
        print("\n选择的重要POI类型：")
        print(f"{'编码':<10}{'重要性':<10}{'大类':<15}{'中类':<20}{'小类':<25}")
        print("-" * 80)

        for typecode, importance in self.selected_poi_types[:20]:  # 只打印前20个
            # 查找POI类型的名称
            matches = self.poi_categories[self.poi_categories['NEW_TYPE'] == typecode]
            if len(matches) > 0:
                row = matches.iloc[0]
                print(f"{typecode:<10}{importance:.6f}  {row['大类']:<15}{row['中类']:<20}{row['小类']:<25}")
            else:
                print(f"{typecode:<10}{importance:.6f}  {'未找到':<15}{'未找到':<20}{'未找到':<25}")

        # 可视化特征重要性
        self._visualize_feature_importance()

        # 保存选择的POI类型
        self._save_selected_poi_types()

        # 分析季度间的变化
        self._analyze_quarterly_changes(common_stations)

        return True

    def _visualize_feature_importance(self):
        """可视化特征重要性"""
        if not self.selected_poi_types:
            return

        # 创建输出目录
        os.makedirs('output', exist_ok=True)

        # 提取前20个重要特征
        top_features = self.selected_poi_types[:20]

        # 获取POI类型名称
        feature_names = []
        for typecode, _ in top_features:
            matches = self.poi_categories[self.poi_categories['NEW_TYPE'] == typecode]
            if len(matches) > 0:
                row = matches.iloc[0]
                feature_names.append(f"{typecode} ({row['小类']})")
            else:
                feature_names.append(typecode)

        # 提取重要性值
        importances = [importance for _, importance in top_features]

        # 创建水平条形图
        plt.figure(figsize=(12, 10))
        y_pos = np.arange(len(feature_names))

        plt.barh(y_pos, importances, align='center')
        plt.yticks(y_pos, feature_names)
        plt.xlabel('特征重要性')
        plt.title('POI类型特征重要性（前20个）- 两季度模型')
        plt.tight_layout()

        # 保存图片
        plt.savefig('output/poi_feature_importance.png', dpi=300)
        print(f"特征重要性可视化已保存到 output/poi_feature_importance.png")

        plt.close()

    def _save_selected_poi_types(self):
        """保存选择的POI类型"""
        if not self.selected_poi_types:
            return

        # 创建输出目录
        os.makedirs('output', exist_ok=True)

        # 保存为CSV文件
        with open('output/selected_poi_types.csv', 'w', encoding='utf-8') as f:
            f.write("typecode,importance,large_category,medium_category,small_category\n")

            for typecode, importance in self.selected_poi_types:
                # 查找POI类型的名称
                matches = self.poi_categories[self.poi_categories['NEW_TYPE'] == typecode]
                if len(matches) > 0:
                    row = matches.iloc[0]
                    f.write(f"{typecode},{importance:.6f},{row['大类']},{row['中类']},{row['小类']}\n")
                else:
                    f.write(f"{typecode},{importance:.6f},未找到,未找到,未找到\n")

        # 保存为weightPOI格式
        with open('output/weightPOI_optimized', 'w', encoding='utf-8') as f:
            for typecode, _ in self.selected_poi_types:
                f.write(f"{typecode}\n")

        print(f"已将选择的POI类型保存到 output/selected_poi_types.csv 和 output/weightPOI_optimized")

    def _analyze_quarterly_changes(self, common_stations):
        """分析季度间的变化"""
        print("\n分析季度间的变化...")

        # 计算每个场站的营业额增长率
        growth_rates = {}
        for station in common_stations:
            q1_revenue = self.station_revenues_q1[station]
            q2_revenue = self.station_revenues_q2[station]
            growth_rate = (q2_revenue - q1_revenue) / max(1, q1_revenue) * 100  # 转换为百分比
            growth_rates[station] = growth_rate

        # 找出增长最快和下降最快的场站
        top_growing = sorted(growth_rates.items(), key=lambda x: x[1], reverse=True)[:5]
        top_declining = sorted(growth_rates.items(), key=lambda x: x[1])[:5]

        # 打印增长最快的场站
        print("\n增长最快的场站：")
        print(f"{'场站名称':<30}{'Q1营业额':<15}{'Q2营业额':<15}{'增长率':<10}")
        print("-" * 70)
        for station, growth_rate in top_growing:
            q1_revenue = self.station_revenues_q1[station]
            q2_revenue = self.station_revenues_q2[station]
            print(f"{station:<30}{q1_revenue:<15.2f}{q2_revenue:<15.2f}{growth_rate:<10.2f}%")

        # 打印下降最快的场站
        print("\n下降最快的场站：")
        print(f"{'场站名称':<30}{'Q1营业额':<15}{'Q2营业额':<15}{'增长率':<10}")
        print("-" * 70)
        for station, growth_rate in top_declining:
            q1_revenue = self.station_revenues_q1[station]
            q2_revenue = self.station_revenues_q2[station]
            print(f"{station:<30}{q1_revenue:<15.2f}{q2_revenue:<15.2f}{growth_rate:<10.2f}%")

        # 创建季度营业额对比图
        plt.figure(figsize=(12, 8))

        # 选择前10个场站进行可视化
        top_stations = sorted(common_stations, key=lambda x: self.station_revenues_q2[x], reverse=True)[:10]

        x = np.arange(len(top_stations))
        width = 0.35

        q1_revenues = [self.station_revenues_q1[station] for station in top_stations]
        q2_revenues = [self.station_revenues_q2[station] for station in top_stations]

        plt.bar(x - width/2, q1_revenues, width, label='Q1 2025')
        plt.bar(x + width/2, q2_revenues, width, label='Q2 2025')

        plt.xlabel('场站')
        plt.ylabel('营业额')
        plt.title('前10个场站的季度营业额对比')
        plt.xticks(x, [station[:10] + '...' if len(station) > 10 else station for station in top_stations], rotation=45)
        plt.legend()
        plt.tight_layout()

        # 保存图片
        plt.savefig('output/quarterly_revenue_comparison.png', dpi=300)
        print(f"季度营业额对比图已保存到 output/quarterly_revenue_comparison.png")

        plt.close()

def main():
    """主函数"""
    print("POI数据获取与类型选择优化工具")
    print("=" * 80)

    # 初始化优化器
    optimizer = POIOptimizer()

    # 加载数据
    if not optimizer.load_data():
        print("加载数据失败，程序退出")
        return

    # 显示主菜单
    while True:
        print("\n主菜单：")
        print("1. 分析当前POI数据覆盖情况")
        print("2. 优化POI类型选择")
        print("3. 退出")

        choice = input("\n请选择操作 [1-3]: ")

        if choice == '1':
            # 分析当前POI数据的覆盖情况
            optimizer.analyze_poi_coverage()

        elif choice == '2':
            # 基于两个季度的数据优化POI类型选择
            optimizer.optimize_poi_type_selection()

        elif choice == '3':
            print("\n程序已退出")
            break

        else:
            print("\n无效的选择，请重新输入")

if __name__ == "__main__":
    main()
