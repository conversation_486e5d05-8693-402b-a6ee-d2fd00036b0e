#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现有充电场站综合分析总结报告
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def generate_executive_summary():
    """生成执行摘要"""
    print("正在生成执行摘要...")
    
    # 读取数据
    comprehensive_df = pd.read_csv('output/comprehensive_report.csv')
    metrics_df = pd.read_csv('output/station_metrics.csv')
    stations_df = pd.read_csv('resource/final_stations_107.csv')
    
    report = []
    report.append("=" * 100)
    report.append("现有充电场站综合分析 - 执行摘要")
    report.append("=" * 100)
    report.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"分析范围: {len(stations_df)} 个充电场站")
    report.append("")
    
    # 关键发现
    report.append("【关键发现】")
    report.append("")
    
    # 1. 整体表现
    if not metrics_df.empty:
        total_revenue = metrics_df['total_revenue'].sum()
        total_orders = metrics_df['total_orders'].sum()
        avg_score = comprehensive_df['combined_score'].mean()
        
        report.append(f"1. 整体运营表现:")
        report.append(f"   • 总收益: ¥{total_revenue:,.0f}")
        report.append(f"   • 总订单: {total_orders:,.0f}")
        report.append(f"   • 平均综合评分: {avg_score:.2f}")
        report.append("")
    
    # 2. 表现分层
    excellent = len(comprehensive_df[comprehensive_df['combined_score'] >= 20])
    good = len(comprehensive_df[(comprehensive_df['combined_score'] >= 10) & 
                               (comprehensive_df['combined_score'] < 20)])
    average = len(comprehensive_df[(comprehensive_df['combined_score'] >= 5) & 
                                  (comprehensive_df['combined_score'] < 10)])
    poor = len(comprehensive_df[comprehensive_df['combined_score'] < 5])
    
    report.append(f"2. 场站表现分层:")
    report.append(f"   • 优秀场站 (≥20分): {excellent} 个 ({excellent/len(comprehensive_df)*100:.1f}%)")
    report.append(f"   • 良好场站 (10-20分): {good} 个 ({good/len(comprehensive_df)*100:.1f}%)")
    report.append(f"   • 一般场站 (5-10分): {average} 个 ({average/len(comprehensive_df)*100:.1f}%)")
    report.append(f"   • 较差场站 (<5分): {poor} 个 ({poor/len(comprehensive_df)*100:.1f}%)")
    report.append("")
    
    # 3. 运营商表现
    operator_performance = {}
    for _, row in comprehensive_df.iterrows():
        station_name = row['station_name']
        score = row['combined_score']
        
        if '易佳电' in station_name:
            operator = '易佳电'
        elif '星桩互联' in station_name:
            operator = '星桩互联'
        elif '深圳呼电' in station_name:
            operator = '深圳呼电'
        elif '合肥市公交站' in station_name:
            operator = '合肥公交'
        else:
            operator = '其他'
        
        if operator not in operator_performance:
            operator_performance[operator] = []
        operator_performance[operator].append(score)
    
    report.append(f"3. 运营商表现对比:")
    for operator, scores in sorted(operator_performance.items(), 
                                 key=lambda x: np.mean(x[1]), reverse=True):
        if len(scores) >= 3:  # 至少3个场站
            avg_score = np.mean(scores)
            report.append(f"   • {operator}: 平均{avg_score:.2f}分 ({len(scores)}个场站)")
    report.append("")
    
    # 4. 主要问题
    report.append(f"4. 主要问题识别:")
    
    # POI环境好但运营差
    poi_good_perf_bad = comprehensive_df[
        (comprehensive_df['poi_score'] > 2.5) & 
        (comprehensive_df['performance_score'] < 15)
    ]
    
    # 运营好但POI差
    perf_good_poi_bad = comprehensive_df[
        (comprehensive_df['poi_score'] < 2.0) & 
        (comprehensive_df['performance_score'] > 20)
    ]
    
    # 低利用率场站
    if not metrics_df.empty:
        low_utilization = metrics_df[metrics_df['equipment_utilization'] < 0.5]
        
        report.append(f"   • POI环境好但运营差: {len(poi_good_perf_bad)} 个场站")
        report.append(f"   • 运营好但POI环境差: {len(perf_good_poi_bad)} 个场站")
        report.append(f"   • 设备利用率过低: {len(low_utilization)} 个场站")
    report.append("")
    
    # 改进建议
    report.append("【核心改进建议】")
    report.append("")
    
    report.append("1. 立即行动项目:")
    report.append(f"   • 关闭或重大改造综合评分<2分的{len(comprehensive_df[comprehensive_df['combined_score'] < 2])}个场站")
    report.append(f"   • 优化{len(poi_good_perf_bad)}个POI环境好但运营差的场站运营策略")
    if not metrics_df.empty:
        very_low_util = metrics_df[metrics_df['equipment_utilization'] < 0.2]
        report.append(f"   • 调整{len(very_low_util)}个极低利用率场站的运营模式")
    report.append("")
    
    report.append("2. 中期优化项目:")
    report.append(f"   • 全面评估{len(comprehensive_df[(comprehensive_df['combined_score'] >= 2) & (comprehensive_df['combined_score'] < 5)])}个综合评分2-5分的场站")
    report.append(f"   • 考虑{len(perf_good_poi_bad)}个运营好但POI差场站的选址优化")
    report.append("   • 建立数据质量监控体系，解决利用率异常问题")
    report.append("")
    
    report.append("3. 长期发展策略:")
    report.append("   • 基于优秀场站经验，制定标准化运营模式")
    report.append("   • 建立场站表现监控和预警机制")
    report.append("   • 优化新场站选址决策流程")
    report.append("")
    
    # 投资回报分析
    if not metrics_df.empty:
        report.append("【投资回报分析】")
        report.append("")

        # 计算不同评分段的平均收益
        merged_df = pd.merge(comprehensive_df, metrics_df, on='station_name', how='inner')

        if 'total_revenue' in merged_df.columns:
            excellent_revenue = merged_df[merged_df['combined_score'] >= 20]['total_revenue'].mean()
            good_revenue = merged_df[(merged_df['combined_score'] >= 10) &
                                   (merged_df['combined_score'] < 20)]['total_revenue'].mean()
            poor_revenue = merged_df[merged_df['combined_score'] < 5]['total_revenue'].mean()

            report.append(f"不同评分段场站的平均收益:")
            if not pd.isna(excellent_revenue):
                report.append(f"• 优秀场站 (≥20分): ¥{excellent_revenue:,.0f}")
            if not pd.isna(good_revenue):
                report.append(f"• 良好场站 (10-20分): ¥{good_revenue:,.0f}")
            if not pd.isna(poor_revenue):
                report.append(f"• 较差场站 (<5分): ¥{poor_revenue:,.0f}")
            report.append("")

            if not pd.isna(excellent_revenue) and not pd.isna(poor_revenue):
                improvement_potential = (excellent_revenue - poor_revenue) * poor
                report.append(f"改进潜力估算:")
                report.append(f"• 如将{poor}个较差场站提升到优秀水平")
                report.append(f"• 预计可增加年收益: ¥{improvement_potential:,.0f}")
                report.append("")
    
    # 下一步行动计划
    report.append("【下一步行动计划】")
    report.append("")
    
    report.append("第一阶段 (1-3个月):")
    report.append("1. 对综合评分<2分的场站进行详细评估")
    report.append("2. 启动POI环境好但运营差场站的运营优化项目")
    report.append("3. 建立场站表现监控仪表板")
    report.append("")
    
    report.append("第二阶段 (3-6个月):")
    report.append("1. 实施运营优化措施并监控效果")
    report.append("2. 评估选址优化的可行性和成本")
    report.append("3. 制定标准化运营流程")
    report.append("")
    
    report.append("第三阶段 (6-12个月):")
    report.append("1. 基于改进效果调整整体策略")
    report.append("2. 推广成功经验到其他场站")
    report.append("3. 建立长期表现预测模型")
    report.append("")
    
    # 保存报告
    with open('output/executive_summary.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("✓ 执行摘要已保存到 output/executive_summary.txt")

def create_action_plan():
    """创建具体行动计划"""
    print("正在生成具体行动计划...")
    
    comprehensive_df = pd.read_csv('output/comprehensive_report.csv')
    metrics_df = pd.read_csv('output/station_metrics.csv')
    
    # 识别不同类型的问题场站
    immediate_action = comprehensive_df[comprehensive_df['combined_score'] < 2]
    high_potential = comprehensive_df[
        (comprehensive_df['poi_score'] > 2.5) & 
        (comprehensive_df['performance_score'] < 15)
    ]
    location_issues = comprehensive_df[
        (comprehensive_df['poi_score'] < 2.0) & 
        (comprehensive_df['performance_score'] > 20)
    ]
    
    action_plan = []
    action_plan.append("=" * 80)
    action_plan.append("充电场站改进行动计划")
    action_plan.append("=" * 80)
    action_plan.append(f"制定时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    action_plan.append("")
    
    # 立即行动清单
    action_plan.append("【立即行动清单】")
    action_plan.append("")
    
    action_plan.append(f"1. 关闭/改造候选场站 ({len(immediate_action)}个):")
    for i, (_, station) in enumerate(immediate_action.head(10).iterrows(), 1):
        action_plan.append(f"   {i:2d}. {station['station_name'][:50]:50s} (评分: {station['combined_score']:.2f})")
    action_plan.append("")
    
    action_plan.append(f"2. 运营优化重点场站 ({len(high_potential)}个):")
    for i, (_, station) in enumerate(high_potential.head(10).iterrows(), 1):
        action_plan.append(f"   {i:2d}. {station['station_name'][:50]:50s} (POI: {station['poi_score']:.2f})")
    action_plan.append("")
    
    action_plan.append(f"3. 选址优化考虑场站 ({len(location_issues)}个):")
    for i, (_, station) in enumerate(location_issues.head(5).iterrows(), 1):
        action_plan.append(f"   {i:2d}. {station['station_name'][:50]:50s} (运营: {station['performance_score']:.2f})")
    action_plan.append("")
    
    # 具体改进措施
    action_plan.append("【具体改进措施】")
    action_plan.append("")
    
    action_plan.append("运营优化措施:")
    action_plan.append("• 调整充电价格策略，提高价格竞争力")
    action_plan.append("• 优化充电桩维护计划，确保设备可用性")
    action_plan.append("• 加强营销推广，提高用户认知度")
    action_plan.append("• 改善用户体验，如增加便民设施")
    action_plan.append("• 建立用户反馈机制，及时解决问题")
    action_plan.append("")
    
    action_plan.append("选址优化措施:")
    action_plan.append("• 评估迁移到POI密度更高区域的可行性")
    action_plan.append("• 与周边商业设施建立合作关系")
    action_plan.append("• 改善场站周边的指示标识")
    action_plan.append("• 考虑增加配套服务设施")
    action_plan.append("")
    
    # 监控指标
    action_plan.append("【关键监控指标】")
    action_plan.append("")
    action_plan.append("月度监控:")
    action_plan.append("• 设备利用率变化")
    action_plan.append("• 月收益增长率")
    action_plan.append("• 用户满意度评分")
    action_plan.append("• 设备故障率")
    action_plan.append("")
    
    action_plan.append("季度评估:")
    action_plan.append("• 综合评分变化")
    action_plan.append("• ROI改善情况")
    action_plan.append("• 市场份额变化")
    action_plan.append("• 竞争对手分析")
    action_plan.append("")
    
    # 保存行动计划
    with open('output/action_plan.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(action_plan))
    
    print("✓ 行动计划已保存到 output/action_plan.txt")

def main():
    """主函数"""
    print("开始生成综合总结报告...")
    
    # 确保输出目录存在
    os.makedirs('output', exist_ok=True)
    
    # 生成报告
    generate_executive_summary()
    create_action_plan()
    
    print("\n" + "="*80)
    print("现有充电场站综合分析完成！")
    print("="*80)
    print("生成的分析文件:")
    print("• output/executive_summary.txt - 执行摘要")
    print("• output/action_plan.txt - 具体行动计划")
    print("• output/top_performers_report.txt - 优秀场站分析")
    print("• output/improvement_analysis_report.txt - 改进机会分析")
    print("• output/performance_analysis.png - 运营表现图表")
    print("• output/score_analysis.png - 评分分析图表")
    print("• output/improvement_analysis.png - 改进机会图表")
    print("="*80)

if __name__ == "__main__":
    main()
