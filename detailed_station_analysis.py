#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的现有充电场站分析和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_performance_analysis():
    """创建运营表现分析"""
    print("正在生成运营表现分析...")
    
    # 读取数据
    metrics_df = pd.read_csv('output/station_metrics.csv')
    comprehensive_df = pd.read_csv('output/comprehensive_report.csv')
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('充电场站运营表现分析', fontsize=16, fontweight='bold')
    
    # 1. 收益分布
    ax1 = axes[0, 0]
    metrics_df['total_revenue'].hist(bins=30, ax=ax1, alpha=0.7, color='skyblue')
    ax1.set_title('场站收益分布')
    ax1.set_xlabel('总收益 (¥)')
    ax1.set_ylabel('场站数量')
    ax1.axvline(metrics_df['total_revenue'].mean(), color='red', linestyle='--', 
                label=f'平均值: ¥{metrics_df["total_revenue"].mean():,.0f}')
    ax1.legend()
    
    # 2. 设备利用率分布
    ax2 = axes[0, 1]
    # 过滤异常值
    utilization_filtered = metrics_df[metrics_df['equipment_utilization'] < 50]['equipment_utilization']
    utilization_filtered.hist(bins=30, ax=ax2, alpha=0.7, color='lightgreen')
    ax2.set_title('设备利用率分布 (过滤异常值)')
    ax2.set_xlabel('设备利用率')
    ax2.set_ylabel('场站数量')
    ax2.axvline(utilization_filtered.mean(), color='red', linestyle='--',
                label=f'平均值: {utilization_filtered.mean():.2f}')
    ax2.legend()
    
    # 3. 收益 vs 订单量
    ax3 = axes[1, 0]
    ax3.scatter(metrics_df['total_orders'], metrics_df['total_revenue'], 
                alpha=0.6, color='orange')
    ax3.set_title('收益 vs 订单量关系')
    ax3.set_xlabel('总订单量')
    ax3.set_ylabel('总收益 (¥)')
    
    # 添加趋势线
    z = np.polyfit(metrics_df['total_orders'], metrics_df['total_revenue'], 1)
    p = np.poly1d(z)
    ax3.plot(metrics_df['total_orders'], p(metrics_df['total_orders']), "r--", alpha=0.8)
    
    # 4. 设备数量 vs 收益
    ax4 = axes[1, 1]
    ax4.scatter(metrics_df['equipment_count'], metrics_df['total_revenue'], 
                alpha=0.6, color='purple')
    ax4.set_title('设备数量 vs 收益关系')
    ax4.set_xlabel('设备数量')
    ax4.set_ylabel('总收益 (¥)')
    
    plt.tight_layout()
    plt.savefig('output/performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 运营表现分析图表已保存到 output/performance_analysis.png")

def create_score_analysis():
    """创建评分分析"""
    print("正在生成评分分析...")
    
    comprehensive_df = pd.read_csv('output/comprehensive_report.csv')
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('充电场站评分分析', fontsize=16, fontweight='bold')
    
    # 1. 综合评分分布
    ax1 = axes[0, 0]
    comprehensive_df['combined_score'].hist(bins=30, ax=ax1, alpha=0.7, color='lightcoral')
    ax1.set_title('综合评分分布')
    ax1.set_xlabel('综合评分')
    ax1.set_ylabel('场站数量')
    ax1.axvline(comprehensive_df['combined_score'].mean(), color='red', linestyle='--',
                label=f'平均值: {comprehensive_df["combined_score"].mean():.2f}')
    ax1.legend()
    
    # 2. POI评分 vs 运营评分
    ax2 = axes[0, 1]
    valid_data = comprehensive_df.dropna(subset=['poi_score', 'performance_score'])
    ax2.scatter(valid_data['poi_score'], valid_data['performance_score'], 
                alpha=0.6, color='green')
    ax2.set_title('POI评分 vs 运营评分')
    ax2.set_xlabel('POI评分')
    ax2.set_ylabel('运营评分')
    
    # 3. 评分等级分布
    ax3 = axes[1, 0]
    score_ranges = ['<5分', '5-10分', '10-20分', '≥20分']
    score_counts = [
        len(comprehensive_df[comprehensive_df['combined_score'] < 5]),
        len(comprehensive_df[(comprehensive_df['combined_score'] >= 5) & 
                           (comprehensive_df['combined_score'] < 10)]),
        len(comprehensive_df[(comprehensive_df['combined_score'] >= 10) & 
                           (comprehensive_df['combined_score'] < 20)]),
        len(comprehensive_df[comprehensive_df['combined_score'] >= 20])
    ]
    
    colors = ['red', 'orange', 'yellow', 'green']
    ax3.pie(score_counts, labels=score_ranges, colors=colors, autopct='%1.1f%%')
    ax3.set_title('评分等级分布')
    
    # 4. 运营商表现对比
    ax4 = axes[1, 1]
    operator_scores = {}
    for _, row in comprehensive_df.iterrows():
        station_name = row['station_name']
        score = row['combined_score']
        
        if '易佳电' in station_name:
            operator = '易佳电'
        elif '星桩互联' in station_name:
            operator = '星桩互联'
        elif '深圳呼电' in station_name:
            operator = '深圳呼电'
        elif '合肥市公交站' in station_name:
            operator = '合肥公交'
        else:
            operator = '其他'
        
        if operator not in operator_scores:
            operator_scores[operator] = []
        operator_scores[operator].append(score)
    
    # 只显示有足够数据的运营商
    filtered_operators = {k: v for k, v in operator_scores.items() if len(v) >= 3}
    
    operators = list(filtered_operators.keys())
    avg_scores = [np.mean(filtered_operators[op]) for op in operators]
    
    bars = ax4.bar(operators, avg_scores, color=['blue', 'green', 'orange', 'red'])
    ax4.set_title('运营商平均评分对比')
    ax4.set_ylabel('平均综合评分')
    ax4.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, score in zip(bars, avg_scores):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{score:.2f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('output/score_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 评分分析图表已保存到 output/score_analysis.png")

def create_top_performers_report():
    """创建优秀场站报告"""
    print("正在生成优秀场站报告...")
    
    comprehensive_df = pd.read_csv('output/comprehensive_report.csv')
    metrics_df = pd.read_csv('output/station_metrics.csv')
    
    # 合并数据
    merged_df = pd.merge(comprehensive_df, metrics_df, on='station_name', how='left')
    
    # 创建报告
    report = []
    report.append("=" * 80)
    report.append("优秀充电场站深度分析报告")
    report.append("=" * 80)
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 综合表现前5名
    report.append("【综合表现TOP 5场站详细分析】")
    report.append("")
    
    top_5 = merged_df.nlargest(5, 'combined_score')
    
    for i, (_, station) in enumerate(top_5.iterrows(), 1):
        report.append(f"{i}. {station['station_name']}")
        report.append(f"   综合评分: {station['combined_score']:.2f}")
        report.append(f"   POI评分: {station.get('poi_score', 'N/A'):.2f}")
        report.append(f"   运营评分: {station.get('performance_score', 'N/A'):.2f}")
        
        if not pd.isna(station.get('total_revenue')):
            report.append(f"   总收益: ¥{station['total_revenue']:,.0f}")
            report.append(f"   总订单: {station['total_orders']:,.0f}")
            report.append(f"   设备数量: {station['equipment_count']:.0f}")
            report.append(f"   设备利用率: {station['equipment_utilization']:.2%}")
            report.append(f"   日均收益: ¥{station['avg_daily_revenue']:.2f}")
        
        report.append("")
    
    # 各维度表现优秀的场站
    report.append("【各维度表现优秀场站】")
    report.append("")
    
    # POI环境最佳
    report.append("POI环境最佳场站 (前5名):")
    top_poi = comprehensive_df.nlargest(5, 'poi_score')
    for i, (_, station) in enumerate(top_poi.iterrows(), 1):
        report.append(f"  {i}. {station['station_name'][:40]:40s} POI评分: {station['poi_score']:.2f}")
    report.append("")
    
    # 运营表现最佳
    report.append("运营表现最佳场站 (前5名):")
    top_perf = comprehensive_df.dropna(subset=['performance_score']).nlargest(5, 'performance_score')
    for i, (_, station) in enumerate(top_perf.iterrows(), 1):
        report.append(f"  {i}. {station['station_name'][:40]:40s} 运营评分: {station['performance_score']:.2f}")
    report.append("")
    
    # 收益最高
    if not metrics_df.empty:
        report.append("收益最高场站 (前5名):")
        top_revenue = metrics_df.nlargest(5, 'total_revenue')
        for i, (_, station) in enumerate(top_revenue.iterrows(), 1):
            report.append(f"  {i}. {station['station_name'][:40]:40s} 收益: ¥{station['total_revenue']:,.0f}")
        report.append("")
    
    # 成功因素分析
    report.append("【成功因素分析】")
    report.append("")
    
    # 分析综合评分前10名的共同特征
    top_10 = merged_df.nlargest(10, 'combined_score')

    if not top_10.empty:
        avg_poi = top_10['poi_score'].mean()
        avg_perf = top_10['performance_score'].mean()

        report.append("优秀场站的共同特征:")
        report.append(f"• 平均POI评分: {avg_poi:.2f}")
        report.append(f"• 平均运营评分: {avg_perf:.2f}")

        # 只有当列存在时才计算
        if 'equipment_count' in top_10.columns:
            avg_equipment = top_10['equipment_count'].mean()
            report.append(f"• 平均设备数量: {avg_equipment:.1f}")

        if 'equipment_utilization' in top_10.columns:
            avg_utilization = top_10['equipment_utilization'].mean()
            report.append(f"• 平均设备利用率: {avg_utilization:.2%}")

        report.append("")
    
    # 保存报告
    with open('output/top_performers_report.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("✓ 优秀场站报告已保存到 output/top_performers_report.txt")

def main():
    """主函数"""
    print("开始生成详细分析报告...")
    
    # 确保输出目录存在
    os.makedirs('output', exist_ok=True)
    
    # 生成各种分析
    create_performance_analysis()
    create_score_analysis()
    create_top_performers_report()
    
    print("\n" + "="*60)
    print("详细分析报告生成完成！")
    print("生成的文件:")
    print("• output/performance_analysis.png - 运营表现分析图表")
    print("• output/score_analysis.png - 评分分析图表")
    print("• output/top_performers_report.txt - 优秀场站详细报告")
    print("="*60)

if __name__ == "__main__":
    main()
