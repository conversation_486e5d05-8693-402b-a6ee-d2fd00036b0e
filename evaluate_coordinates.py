#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电站坐标评估工具

此脚本用于评估给定坐标的充电站适合度，包括：
1. 从坐标获取周边POI数据
2. 计算POI评分
3. 预测业绩表现
4. 计算综合评分
5. 可视化评估结果
"""

import os
import sys
import json
import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
from datetime import datetime
from station_analysis import StationAnalyzer
import config

# 设置中文字体
try:
    import matplotlib_chinese_fonts
except ImportError:
    print("警告: 未找到中文字体配置文件，图表中的中文可能无法正确显示")
    print("建议先运行 python fix_font_display.py 修复字体问题")
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

def fetch_poi_data_from_coordinates(longitude, latitude, radius=1000, api_key=None):
    """
    从坐标获取周边POI数据

    参数:
        longitude: 经度
        latitude: 纬度
        radius: 搜索半径（米）
        api_key: 高德地图API密钥，如果为None则从配置文件获取

    返回:
        POI数据字典
    """
    # 优先级：传入的API密钥 > 配置文件中的API密钥 > 环境变量中的API密钥
    if api_key is None or api_key == '':
        api_key = config.GAODE_API_KEY or os.environ.get('AMAP_API_KEY')

        # 如果没有设置API密钥，抛出异常
        if not api_key:
            raise ValueError("未设置高德地图API密钥，请在配置文件中添加")

    # 构建API请求URL
    url = f"https://restapi.amap.com/v3/place/around?key={api_key}&location={longitude},{latitude}&radius={radius}&extensions=all"

    try:
        # 发送请求并获取响应
        response = requests.get(url)
        data = response.json()

        if data.get('status') == '1':  # 请求成功
            # 处理响应数据
            pois = data.get('pois', [])

            # 构建与系统兼容的POI数据格式
            poi_data = {
                'data': {
                    'pois': pois,
                    'longitude': longitude,
                    'latitude': latitude
                }
            }

            print(f"已获取坐标 ({longitude}, {latitude}) 周边 {len(pois)} 个POI")

            # 保存POI数据到文件
            save_poi_data(poi_data, longitude, latitude)

            return poi_data['data']
        else:
            print(f"获取POI数据失败: {data.get('info', '未知错误')}")
            return load_sample_poi_data()
    except Exception as e:
        print(f"获取POI数据时出错: {e}")
        return load_sample_poi_data()

def load_sample_poi_data():
    """
    加载示例POI数据

    返回:
        示例POI数据
    """
    print("正在加载示例POI数据...")

    # 创建一个简单的示例POI数据
    sample_data = {
        'pois': [
            {'typecode': '010100', 'name': '示例商业区'},
            {'typecode': '060400', 'name': '示例超市'},
            {'typecode': '060700', 'name': '示例购物中心'},
            {'typecode': '070000', 'name': '示例住宿服务'},
            {'typecode': '120200', 'name': '示例住宅区'},
            {'typecode': '150500', 'name': '示例停车场'},
            {'typecode': '150700', 'name': '示例公交站'},
            {'typecode': '150900', 'name': '示例充电站'},
            {'typecode': '170100', 'name': '示例公司'},
            {'typecode': '190304', 'name': '示例学校'}
        ],
        'longitude': 121.473701,
        'latitude': 31.230416
    }

    # 如果有实际的POI数据文件，尝试加载
    poi_dir = 'raw_poi_data'
    if os.path.exists(poi_dir) and os.path.isdir(poi_dir):
        files = os.listdir(poi_dir)
        json_files = [f for f in files if f.endswith('.json')]

        if json_files:
            sample_file = os.path.join(poi_dir, json_files[0])
            print(f"尝试使用 {sample_file} 作为示例数据")

            try:
                with open(sample_file, 'r', encoding='utf-8') as f:
                    poi_data = json.load(f)
                    if 'data' in poi_data and 'pois' in poi_data['data']:
                        return poi_data['data']
                    else:
                        print("文件格式不符合要求，使用默认示例数据")
            except Exception as e:
                print(f"加载示例数据时出错: {e}")

    print("使用默认示例POI数据")
    return sample_data

def save_poi_data(poi_data, longitude, latitude):
    """
    保存POI数据到文件

    参数:
        poi_data: POI数据
        longitude: 经度
        latitude: 纬度
    """
    # 从配置文件获取POI数据目录
    poi_dir = config.POI_DATA_DIR
    os.makedirs(poi_dir, exist_ok=True)

    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{poi_dir}/新坐标_{longitude}_{latitude}_{timestamp}.json"

    # 保存到文件
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(poi_data, f, ensure_ascii=False, indent=2)
        print(f"POI数据已保存到 {filename}")
    except Exception as e:
        print(f"保存POI数据时出错: {e}")

def evaluate_coordinates(longitude, latitude, analyzer, api_key=None, radius=1000):
    """
    评估坐标的充电站适合度

    参数:
        longitude: 经度
        latitude: 纬度
        analyzer: StationAnalyzer实例
        api_key: 高德地图API密钥
        radius: POI搜索半径（米）

    返回:
        评估结果字典
    """
    print(f"正在评估坐标 ({longitude}, {latitude}) 的充电站适合度...")

    # 1. 获取坐标周边POI数据
    poi_data = fetch_poi_data_from_coordinates(longitude, latitude, radius, api_key)

    if poi_data is None or not poi_data.get('pois'):
        print(f"无法获取坐标 ({longitude}, {latitude}) 的POI数据")
        return None

    # 2. 在POI数据中添加坐标信息，确保predict_new_station_score能够计算距离
    poi_data['location'] = f"{longitude},{latitude}"

    # 3. 预测新场站得分
    prediction_results = analyzer.predict_new_station_score(poi_data)

    # 4. 添加坐标信息到结果中
    prediction_results['coordinates'] = {
        'longitude': longitude,
        'latitude': latitude
    }

    return prediction_results

def visualize_evaluation_results(prediction_results, analyzer, save_path=None):
    """
    可视化评估结果

    参数:
        prediction_results: 评估结果字典
        analyzer: StationAnalyzer实例
        save_path: 保存图片的路径（如果为None，则显示图片）
    """
    print("正在生成评估结果可视化...")

    # 创建图形
    fig = plt.figure(figsize=(15, 12))

    # 1. 评分对比
    ax1 = fig.add_subplot(221)
    scores = [prediction_results['poi_score']]
    labels = ['POI评分']

    if prediction_results['performance_score'] is not None:
        scores.extend([
            prediction_results['performance_score'],
            prediction_results['combined_score']
        ])
        labels.extend(['业绩评分', '综合评分'])

    bars = ax1.bar(labels, scores, color=['skyblue', 'lightgreen', 'salmon'][:len(scores)])

    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}', ha='center', va='bottom')

    ax1.set_ylim(0, max(scores) * 1.1)
    ax1.set_title('评分对比')
    ax1.grid(axis='y', linestyle='--', alpha=0.7)

    # 2. 相似场站得分对比
    ax2 = fig.add_subplot(222)
    # 适应新的数据结构
    if prediction_results['similar_stations'] and isinstance(prediction_results['similar_stations'][0], dict):
        # 新格式：包含坐标信息的字典
        similar_stations = [station_info['name'] for station_info in prediction_results['similar_stations']][:5]
        similar_scores = [station_info['score'] for station_info in prediction_results['similar_stations']][:5]
    else:
        # 旧格式：元组
        similar_stations = [station for station, _ in prediction_results['similar_stations']][:5]
        similar_scores = [score for _, score in prediction_results['similar_stations']][:5]

    # 添加新场站的得分
    all_stations = ['新场站(预测)'] + similar_stations
    all_scores = [prediction_results['combined_score'] if prediction_results['performance_score'] is not None
                 else prediction_results['poi_score']] + similar_scores

    bars = ax2.bar(all_stations, all_scores, color=['salmon'] + ['skyblue'] * len(similar_scores))

    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}', ha='center', va='bottom')

    ax2.set_ylim(0, max(all_scores) * 1.1)
    ax2.set_title('新场站与相似场站得分对比')
    ax2.set_xticklabels(all_stations, rotation=45, ha='right')
    ax2.grid(axis='y', linestyle='--', alpha=0.7)

    # 3. 预测营业额（如果有）
    if prediction_results['predicted_revenue'] is not None and analyzer.order_analyzer is not None:
        ax3 = fig.add_subplot(223)

        # 获取相似场站的营业额
        similar_revenues = []
        similar_stations_with_revenue = []

        # 适应新的数据结构
        if prediction_results['similar_stations'] and isinstance(prediction_results['similar_stations'][0], dict):
            # 新格式：包含坐标信息的字典
            stations_to_check = [(station_info['name'], station_info) for station_info in prediction_results['similar_stations'][:5]]
        else:
            # 旧格式：元组
            stations_to_check = [(station, None) for station, _ in prediction_results['similar_stations'][:5]]

        for station, station_info in stations_to_check:
            if analyzer.order_analyzer.station_metrics is not None and station in analyzer.order_analyzer.station_metrics.index:
                revenue = analyzer.order_analyzer.station_metrics.loc[station, 'total_revenue']
                similar_revenues.append(revenue)
                similar_stations_with_revenue.append(station)

        if similar_revenues:
            # 添加预测的新场站营业额
            all_stations = ['新场站(预测)'] + similar_stations_with_revenue
            all_revenues = [prediction_results['predicted_revenue']] + similar_revenues

            bars = ax3.bar(all_stations, all_revenues, color=['salmon'] + ['skyblue'] * len(similar_revenues))

            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 100,
                        f'{height:.0f}', ha='center', va='bottom')

            ax3.set_title('预测周营业额与相似场站对比')
            ax3.set_xticklabels(all_stations, rotation=45, ha='right')
            ax3.grid(axis='y', linestyle='--', alpha=0.7)

    # 4. 建议
    ax4 = fig.add_subplot(224)
    ax4.axis('off')  # 不显示坐标轴

    # 构建建议文本
    recommendation_text = f"坐标: ({prediction_results['coordinates']['longitude']}, {prediction_results['coordinates']['latitude']})\n\n"
    recommendation_text += f"POI评分: {prediction_results['poi_score']:.2f}\n"

    # 添加战略价值评分的显示
    if 'strategic_score' in prediction_results:
        recommendation_text += f"战略价值评分: {prediction_results['strategic_score']:.2f}\n"

    if prediction_results['performance_score'] is not None:
        recommendation_text += f"业绩评分: {prediction_results['performance_score']:.2f}\n"
        recommendation_text += f"综合评分: {prediction_results['combined_score']:.2f}\n"

    if prediction_results['predicted_revenue'] is not None:
        recommendation_text += f"预测周营业额: {prediction_results['predicted_revenue']:.2f}元\n"

    recommendation_text += f"\n建议: {prediction_results['recommendation']}建设充电站"

    # 显示建议文本
    ax4.text(0.5, 0.5, recommendation_text, ha='center', va='center', fontsize=12)

    plt.tight_layout()

    # 从配置文件获取输出目录
    output_dir = config.OUTPUT_DIR
    os.makedirs(output_dir, exist_ok=True)

    # 保存或显示图片
    if save_path:
        plt.savefig(save_path, dpi=300)
        print(f"评估结果可视化已保存到 {save_path}")
    else:
        # 使用预测结果中的坐标信息
        longitude = prediction_results['coordinates']['longitude']
        latitude = prediction_results['coordinates']['latitude']
        save_path = f"{output_dir}/坐标评估_{longitude}_{latitude}.png"
        plt.savefig(save_path, dpi=300)
        print(f"评估结果可视化已保存到 {save_path}")

        # 保存评估结果数据为JSON文件
        import json
        json_path = f"{output_dir}/评估结果_{longitude}_{latitude}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(prediction_results, f, ensure_ascii=False, indent=2)
        print(f"评估结果数据已保存到 {json_path}")

    plt.close()

def main():
    """主函数"""
    print("充电站坐标评估工具")
    print("=" * 50)

    # 检查命令行参数
    if len(sys.argv) >= 3:
        try:
            longitude = float(sys.argv[1])
            latitude = float(sys.argv[2])
            radius = int(sys.argv[3]) if len(sys.argv) >= 4 else 1000
        except ValueError:
            print("错误: 经度、纬度和半径必须是数字")
            print("用法: python evaluate_coordinates.py <经度> <纬度> [半径]")
            return
    else:
        # 使用默认坐标（上海市中心）
        longitude = 121.473701
        latitude = 31.230416
        radius = 1000
        print(f"未提供坐标，使用默认坐标: ({longitude}, {latitude}), 半径: {radius}米")

    # 初始化分析器
    analyzer = StationAnalyzer(order_data_file='resource/2025_Q2_10cols_107stations.csv')

    # 加载数据
    analyzer.load_data()

    # 构建POI向量
    analyzer.build_poi_vectors()

    # 计算相似度
    analyzer.calculate_similarity()

    # 构建评分框架
    analyzer.build_scoring_framework()

    # 评估坐标
    prediction_results = evaluate_coordinates(longitude, latitude, analyzer, radius=radius)

    if prediction_results:
        # 可视化评估结果
        visualize_evaluation_results(prediction_results, analyzer)

        # 打印评估结果
        print("\n评估结果:")
        print(f"坐标: ({longitude}, {latitude})")
        print(f"POI评分: {prediction_results['poi_score']:.2f}")

        # 添加战略价值评分的显示
        if 'strategic_score' in prediction_results:
            print(f"战略价值评分: {prediction_results['strategic_score']:.2f}")

        if prediction_results['performance_score'] is not None:
            print(f"业绩评分: {prediction_results['performance_score']:.2f}")
            print(f"综合评分: {prediction_results['combined_score']:.2f}")

        if prediction_results['predicted_revenue'] is not None:
            print(f"预测周营业额: {prediction_results['predicted_revenue']:.2f}元")

        print(f"建议: {prediction_results['recommendation']}建设充电站")

        print("\n最相似的场站:")
        # 适应新的数据结构
        if prediction_results['similar_stations'] and isinstance(prediction_results['similar_stations'][0], dict):
            # 新格式：包含坐标信息和距离的字典
            for station_info in prediction_results['similar_stations']:
                station_name = station_info['name']
                score = station_info['score']
                coords = station_info.get('coordinates')
                distance = station_info.get('distance_km')

                if coords and distance is not None:
                    print(f"- {station_name}: 评分 {score:.2f} (距离: {distance:.2f}公里, 地址: {coords.get('address', '未知')})")
                elif coords:
                    print(f"- {station_name}: 评分 {score:.2f} (坐标: {coords['longitude']:.6f}, {coords['latitude']:.6f}, 地址: {coords.get('address', '未知')})")
                else:
                    print(f"- {station_name}: 评分 {score:.2f} (坐标信息缺失)")
        else:
            # 旧格式：元组
            for station, score in prediction_results['similar_stations']:
                print(f"- {station}: 评分 {score:.2f}")
    else:
        print("评估失败")

if __name__ == "__main__":
    main()
