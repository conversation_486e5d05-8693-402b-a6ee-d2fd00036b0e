#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM响应格式配置

此模块定义了LLM响应的格式配置，用于确保生成内容的格式一致性
"""

# HTML标签配置
HTML_TAGS = {
    "标题": {
        "h2": ["评估概览", "相似场站分析", "建议与结论", "市场分析", "风险评估", "投资回报分析"],
        "h3": ["POI评分深度分析", "战略价值深度分析", "业绩分析与预测", "综合评分深度分析", "关键洞察与建议"],
        "h4": ["同类站点对比分析", "移动充电车专项建议", "风险提示", "运营优化建议"]
    },
    "容器": {
        "div": ["overview-section", "score-section", "financial-section", "recommendation-section", 
                "strategic-analysis", "performance-metrics", "analysis-section", "risk-note"]
    },
    "表格": {
        "表头": ["指标", "得分", "行业基准", "分析", "场站名称", "相似度", "战略评分", "关键特征分析", "POI评分", "业绩评分"],
        "格式": 'border="1"'
    }
}

# 数值格式配置
NUMBER_FORMATS = {
    "评分": {
        "小数位": 2,
        "示例": "48.70"
    },
    "金额": {
        "前缀": "¥",
        "千位分隔": True,
        "示例": "¥7,379.65"
    },
    "百分比": {
        "后缀": "%",
        "小数位": 2,
        "示例": "73.86%"
    }
}

# 各部分标准结构
SECTION_STRUCTURES = {
    "概览": [
        "基础信息",
        "评分分析",
        "财务预测",
        "专业建议"
    ],
    "POI分析": [
        "POI评分解释",
        "当前评分分析",
        "同类站点对比",
        "设备配置建议",
        "运营优化建议"
    ],
    "战略价值分析": [
        "战略价值评分解析",
        "竞品网络对比",
        "移动充电适配建议",
        "风险提示"
    ],
    "业绩分析": [
        "业绩指标分析",
        "相似场站对标分析",
        "移动充电车专项建议",
        "结论"
    ],
    "综合评分分析": [
        "核心指标解析",
        "同类站点对比",
        "移动充电车专项建议",
        "风险提示"
    ],
    "相似场站分析": [
        "相似场站数据表",
        "关键洞察与建议"
    ],
    "建议与结论": [
        "核心评估指标分析",
        "运营优化建议",
        "风险提示",
        "最终结论"
    ]
}

# 获取特定部分的标准结构
def get_section_structure(section_name):
    """
    获取特定部分的标准结构
    
    参数:
        section_name: 部分名称
        
    返回:
        标准结构列表
    """
    return SECTION_STRUCTURES.get(section_name, [])
