#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电站综合分析系统 - 主程序

此脚本整合了POI分析和订单分析，提供全面的充电站评估。
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from station_analysis import StationAnalyzer
from order_analysis import OrderAnalyzer

# 设置中文字体
try:
    import matplotlib_chinese_fonts
except ImportError:
    print("警告: 未找到中文字体配置文件，图表中的中文可能无法正确显示")
    print("建议先运行 python fix_font_display.py 修复字体问题")

def main():
    """主函数"""
    print("=" * 60)
    print("充电站综合分析系统 - 集成POI数据和订单数据")
    print("=" * 60)

    # 确保输出目录存在
    os.makedirs('output', exist_ok=True)

    # 步骤1: 初始化订单分析器
    print("\n步骤1: 初始化订单分析器...")
    order_analyzer = OrderAnalyzer(order_file='resource/2025_Q2_10cols_107stations.csv')

    # 步骤2: 分析订单数据
    print("\n步骤2: 分析订单数据...")
    order_analyzer.analyze_all()

    # 步骤3: 初始化场站分析器
    print("\n步骤3: 初始化场站分析器...")
    station_analyzer = StationAnalyzer(order_data_file='resource/2025_Q2_10cols_107stations.csv')

    # 步骤4: 加载数据
    print("\n步骤4: 加载场站数据...")
    station_analyzer.load_data()

    # 步骤5: 构建POI向量
    print("\n步骤5: 构建POI向量...")
    station_analyzer.build_poi_vectors()

    # 步骤6: 计算场站相似度
    print("\n步骤6: 计算场站相似度...")
    station_analyzer.calculate_similarity()

    # 步骤7: 构建评分框架
    print("\n步骤7: 构建评分框架...")
    scores_df = station_analyzer.build_scoring_fra mework()

    # 步骤8: 可视化评分结果
    print("\n步骤8: 可视化评分结果...")
    station_analyzer.visualize_scores()

    # 步骤9: 生成综合报告
    print("\n步骤9: 生成综合报告...")
    generate_comprehensive_report(station_analyzer, order_analyzer)

    print("\n分析完成! 所有结果已保存到 output 目录")
    print("=" * 60)

def generate_comprehensive_report(station_analyzer, order_analyzer):
    """生成综合分析报告"""
    # 获取评分数据
    if hasattr(station_analyzer, 'scores_df') and station_analyzer.scores_df is not None:
        scores_df = station_analyzer.scores_df
    else:
        print("警告: 未找到场站评分数据，无法生成综合报告")
        return

    # 获取业绩数据
    if hasattr(order_analyzer, 'station_metrics') and order_analyzer.station_metrics is not None:
        metrics_df = order_analyzer.station_metrics
    else:
        print("警告: 未找到场站业绩数据，无法生成综合报告")
        return

    # 合并评分和业绩数据
    report_data = []

    # 检查是否有综合评分
    has_combined_score = 'combined_score' in scores_df.columns

    for _, row in scores_df.iterrows():
        station_name = row['station']

        # 基本评分数据
        station_data = {
            'station_name': station_name,
            'poi_score': row['poi_score'] if has_combined_score else row['score']
        }

        # 添加业绩评分和综合评分（如果有）
        if has_combined_score:
            station_data['performance_score'] = row['performance_score']
            station_data['combined_score'] = row['combined_score']

        # 添加业绩指标（如果有）
        if station_name in metrics_df.index:
            metrics = metrics_df.loc[station_name]
            station_data.update({
                'total_orders': metrics['total_orders'],
                'total_revenue': metrics['total_revenue'],
                'equipment_count': metrics['equipment_count'],
                'equipment_utilization': metrics['equipment_utilization'],
                'avg_daily_revenue': metrics['avg_daily_revenue'],
                'weekend_ratio': metrics['weekend_ratio']
            })

        report_data.append(station_data)

    # 创建报告DataFrame
    report_df = pd.DataFrame(report_data)

    # 保存报告
    report_df.to_csv('output/comprehensive_report.csv', index=False)
    print(f"综合报告已保存到 output/comprehensive_report.csv，包含 {len(report_df)} 个场站的数据")

    # 创建可视化
    create_report_visualizations(report_df, has_combined_score)

def create_report_visualizations(report_df, has_combined_score):
    """创建报告可视化"""
    # 确保有足够的数据
    if len(report_df) < 5:
        print("警告: 数据量太少，无法创建有意义的可视化")
        return

    # 1. 评分分布直方图
    plt.figure(figsize=(12, 8))

    if has_combined_score:
        plt.subplot(2, 2, 1)
        sns.histplot(report_df['poi_score'].dropna(), kde=True, color='blue')
        plt.title('POI评分分布')
        plt.xlabel('评分')
        plt.ylabel('频率')

        plt.subplot(2, 2, 2)
        sns.histplot(report_df['performance_score'].dropna(), kde=True, color='green')
        plt.title('业绩评分分布')
        plt.xlabel('评分')
        plt.ylabel('频率')

        plt.subplot(2, 2, 3)
        sns.histplot(report_df['combined_score'].dropna(), kde=True, color='red')
        plt.title('综合评分分布')
        plt.xlabel('评分')
        plt.ylabel('频率')

        # 评分对比散点图
        plt.subplot(2, 2, 4)
        sns.scatterplot(x='poi_score', y='performance_score', data=report_df, alpha=0.7)
        plt.title('POI评分 vs 业绩评分')
        plt.xlabel('POI评分')
        plt.ylabel('业绩评分')

        # 添加对角线
        xlim = plt.xlim()
        ylim = plt.ylim()
        min_val = min(xlim[0], ylim[0])
        max_val = max(xlim[1], ylim[1])
        plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.3)
    else:
        sns.histplot(report_df['poi_score'].dropna(), kde=True, color='blue')
        plt.title('场站评分分布')
        plt.xlabel('评分')
        plt.ylabel('频率')

    plt.tight_layout()
    plt.savefig('output/score_distributions.png', dpi=300)
    plt.close()

    # 2. 业绩指标与评分的关系
    if 'total_revenue' in report_df.columns:
        plt.figure(figsize=(15, 10))

        # 总营业额与评分的关系
        plt.subplot(2, 2, 1)
        score_col = 'combined_score' if has_combined_score else 'poi_score'
        sns.scatterplot(x=score_col, y='total_revenue', data=report_df, alpha=0.7)
        plt.title(f'{score_col.replace("_", " ").title()} vs 总营业额')
        plt.xlabel(score_col.replace("_", " ").title())
        plt.ylabel('总营业额')

        # 设备利用率与评分的关系
        plt.subplot(2, 2, 2)
        sns.scatterplot(x=score_col, y='equipment_utilization', data=report_df, alpha=0.7)
        plt.title(f'{score_col.replace("_", " ").title()} vs 设备利用率')
        plt.xlabel(score_col.replace("_", " ").title())
        plt.ylabel('设备利用率')

        # 日均营业额与评分的关系
        plt.subplot(2, 2, 3)
        sns.scatterplot(x=score_col, y='avg_daily_revenue', data=report_df, alpha=0.7)
        plt.title(f'{score_col.replace("_", " ").title()} vs 日均营业额')
        plt.xlabel(score_col.replace("_", " ").title())
        plt.ylabel('日均营业额')

        # 周末比例与评分的关系
        plt.subplot(2, 2, 4)
        sns.scatterplot(x=score_col, y='weekend_ratio', data=report_df, alpha=0.7)
        plt.title(f'{score_col.replace("_", " ").title()} vs 周末/工作日比')
        plt.xlabel(score_col.replace("_", " ").title())
        plt.ylabel('周末/工作日比')

        plt.tight_layout()
        plt.savefig('output/score_vs_performance.png', dpi=300)
        plt.close()

        # 3. 顶级场站对比
        top_stations = report_df.sort_values(score_col, ascending=False).head(10)

        plt.figure(figsize=(15, 10))

        # 评分对比
        plt.subplot(2, 1, 1)
        bars = plt.barh(top_stations['station_name'], top_stations[score_col], color='skyblue')
        plt.title(f'评分最高的10个场站 ({score_col.replace("_", " ").title()})')
        plt.xlabel('评分')
        plt.gca().invert_yaxis()  # 使最高分的在顶部

        # 在柱状图上添加数值标签
        for bar in bars:
            width = bar.get_width()
            plt.text(width * 1.01, bar.get_y() + bar.get_height()/2,
                    f'{width:.1f}', va='center')

        # 营业额对比
        plt.subplot(2, 1, 2)
        bars = plt.barh(top_stations['station_name'], top_stations['total_revenue'], color='lightgreen')
        plt.title('评分最高的10个场站的总营业额')
        plt.xlabel('总营业额')
        plt.gca().invert_yaxis()  # 保持与上图相同的顺序

        # 在柱状图上添加数值标签
        for bar in bars:
            width = bar.get_width()
            plt.text(width * 1.01, bar.get_y() + bar.get_height()/2,
                    f'{width:.1f}', va='center')

        plt.tight_layout()
        plt.savefig('output/top_stations_comparison.png', dpi=300)
        plt.close()

if __name__ == "__main__":
    main()
