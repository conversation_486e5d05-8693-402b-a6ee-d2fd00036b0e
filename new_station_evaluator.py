#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
新场站选址评估器
基于改进的POI计算方法，对新的坐标位置进行场站选址评估和分析
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from improved_poi_calculator import ImprovedPOICalculator
from station_analysis import StationAnalyzer
import requests
import time

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

class NewStationEvaluator:
    """新场站选址评估器"""
    
    def __init__(self, api_key='9bc017d3ba3c32eb6fb2d9fe0d224d96', 
                 use_distance_weight=True, decay_factor=1000, max_distance=3000, min_weight=0.1):
        """初始化评估器
        
        参数:
            api_key: 高德地图API密钥
            use_distance_weight: 是否使用距离权重
            decay_factor: 距离衰减因子
            max_distance: 最大有效距离
            min_weight: 最小权重
        """
        self.api_key = api_key
        self.poi_calculator = ImprovedPOICalculator(
            use_distance_weight=use_distance_weight,
            decay_factor=decay_factor,
            max_distance=max_distance,
            min_weight=min_weight
        )
        self.poi_calculator.load_data()
        
        # 存储评估结果
        self.evaluation_results = {}
        
    def get_poi_data_for_location(self, longitude, latitude, radius=3000):
        """获取指定坐标周围的POI数据
        
        参数:
            longitude: 经度
            latitude: 纬度
            radius: 搜索半径（米）
            
        返回:
            POI数据列表
        """
        print(f"正在获取坐标 ({longitude}, {latitude}) 周围 {radius}米 的POI数据...")
        
        # 高德地图POI搜索API
        url = "https://restapi.amap.com/v3/place/around"
        
        all_pois = []
        page = 1
        page_size = 20  # 每页数量
        
        while True:
            params = {
                'key': self.api_key,
                'location': f"{longitude},{latitude}",
                'radius': radius,
                'types': '',  # 获取所有类型
                'page': page,
                'offset': page_size,
                'extensions': 'all'
            }
            
            try:
                response = requests.get(url, params=params, timeout=10)
                data = response.json()
                
                if data['status'] != '1':
                    print(f"API请求失败: {data.get('info', '未知错误')}")
                    break
                
                pois = data.get('pois', [])
                if not pois:
                    break
                
                # 处理POI数据
                for poi in pois:
                    processed_poi = {
                        'name': poi.get('name', ''),
                        'type': poi.get('type', ''),
                        'typecode': poi.get('typecode', ''),
                        'address': poi.get('address', ''),
                        'location': poi.get('location', ''),
                        'distance': poi.get('distance', '0')
                    }
                    all_pois.append(processed_poi)
                
                print(f"已获取第 {page} 页，共 {len(pois)} 个POI")
                
                # 如果返回的POI数量少于页面大小，说明已经是最后一页
                if len(pois) < page_size:
                    break
                
                page += 1
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                print(f"获取POI数据时出错: {e}")
                break
        
        print(f"总共获取到 {len(all_pois)} 个POI")
        return all_pois
    
    def evaluate_new_location(self, longitude, latitude, location_name=None):
        """评估新场站位置
        
        参数:
            longitude: 经度
            latitude: 纬度
            location_name: 位置名称（可选）
            
        返回:
            评估结果字典
        """
        if location_name is None:
            location_name = f"新场站_{longitude}_{latitude}"
        
        print(f"\n开始评估新场站位置: {location_name}")
        print(f"坐标: ({longitude}, {latitude})")
        
        # 1. 获取POI数据
        pois = self.get_poi_data_for_location(longitude, latitude)
        
        if not pois:
            print("未获取到POI数据，无法进行评估")
            return None
        
        # 2. 构建POI向量
        poi_vector = self._build_poi_vector_for_location(pois)
        
        # 3. 计算POI评分
        poi_score = self._calculate_poi_score(poi_vector)
        
        # 4. 计算战略价值评分
        strategic_score = self._calculate_strategic_score(pois, longitude, latitude)
        
        # 5. 找到相似的现有场站
        similar_stations = self._find_similar_stations(poi_vector)
        
        # 6. 预测业绩
        predicted_performance = self._predict_performance(similar_stations)
        
        # 7. 计算综合评分
        if predicted_performance:
            combined_score = (0.5 * poi_score + 
                            0.3 * predicted_performance.get('predicted_score', 0) + 
                            0.2 * strategic_score)
        else:
            combined_score = 0.7 * poi_score + 0.3 * strategic_score
        
        # 8. 生成评估结果
        evaluation_result = {
            'location_name': location_name,
            'longitude': longitude,
            'latitude': latitude,
            'poi_count': len(pois),
            'poi_score': poi_score,
            'strategic_score': strategic_score,
            'combined_score': combined_score,
            'similar_stations': similar_stations,
            'predicted_performance': predicted_performance,
            'poi_vector': poi_vector,
            'pois': pois[:10]  # 保存前10个POI作为示例
        }
        
        self.evaluation_results[location_name] = evaluation_result
        
        print(f"\n评估完成:")
        print(f"POI评分: {poi_score:.2f}")
        print(f"战略价值评分: {strategic_score:.2f}")
        print(f"综合评分: {combined_score:.2f}")
        
        return evaluation_result
    
    def _build_poi_vector_for_location(self, pois):
        """为新位置构建POI向量"""
        from collections import defaultdict
        
        poi_vector = defaultdict(float)
        
        for poi in pois:
            if 'typecode' in poi and poi['typecode']:
                typecode = poi['typecode']
                distance = float(poi.get('distance', 0))
                
                # 计算距离权重
                weight = self.poi_calculator.calculate_distance_weight(distance)
                
                # 获取大类和中类编码
                if len(typecode) == 6:
                    large_category = typecode[:2] + '0000'
                    medium_category = typecode[:4] + '00'
                    
                    # 优先使用最具体的编码
                    if typecode in self.poi_calculator.analyzer.used_poi_codes:
                        poi_vector[typecode] += weight
                    elif medium_category in self.poi_calculator.analyzer.used_poi_codes:
                        poi_vector[medium_category] += weight
                    elif large_category in self.poi_calculator.analyzer.used_poi_codes:
                        poi_vector[large_category] += weight
        
        return dict(poi_vector)
    
    def _calculate_poi_score(self, poi_vector):
        """计算POI评分"""
        # 使用改进的POI计算器的权重
        weights = self.poi_calculator.analyzer.weights or {}
        
        score = 0.0
        for poi_type, count in poi_vector.items():
            weight = weights.get(poi_type, 1.0)
            score += count * weight
        
        return score
    
    def _calculate_strategic_score(self, pois, longitude, latitude):
        """计算战略价值评分"""
        # 简化的战略价值计算
        strategic_score = 50  # 基础分
        
        # 品牌曝光度（商业POI密度）
        commercial_pois = [poi for poi in pois if poi.get('typecode', '').startswith(('05', '06', '08'))]
        if len(commercial_pois) > 20:
            strategic_score += 20
        elif len(commercial_pois) > 10:
            strategic_score += 10
        
        # 交通便利性（交通POI密度）
        transport_pois = [poi for poi in pois if poi.get('typecode', '').startswith('15')]
        if len(transport_pois) > 5:
            strategic_score += 15
        elif len(transport_pois) > 2:
            strategic_score += 8
        
        return min(100, strategic_score)
    
    def _find_similar_stations(self, poi_vector, top_n=5):
        """找到相似的现有场站，包含坐标信息"""
        if self.poi_calculator.improved_vectors is None:
            self.poi_calculator.build_improved_poi_vectors()

        # 计算与现有场站的相似度
        similarities = []

        for station in self.poi_calculator.improved_vectors.index:
            station_vector = self.poi_calculator.improved_vectors.loc[station]

            # 计算余弦相似度
            similarity = self._calculate_cosine_similarity(poi_vector, station_vector)

            # 获取场站坐标信息
            station_coords = self._get_station_coordinates(station)

            similarities.append({
                'name': station,
                'similarity': similarity,
                'coordinates': station_coords
            })

        # 排序并返回最相似的场站
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return similarities[:top_n]

    def _get_station_coordinates(self, station_name):
        """获取场站坐标信息"""
        try:
            # 从场站数据中获取坐标
            station_data_file = 'resource/final_stations_107.csv'
            if os.path.exists(station_data_file):
                import pandas as pd
                station_data = pd.read_csv(station_data_file)

                # 查找对应的场站
                station_row = station_data[station_data['名称'] == station_name]
                if not station_row.empty:
                    longitude = station_row.iloc[0]['高德_经度']
                    latitude = station_row.iloc[0]['高德_纬度']
                    address = station_row.iloc[0]['地址']
                    return {
                        'longitude': longitude,
                        'latitude': latitude,
                        'address': address
                    }
        except Exception as e:
            print(f"获取场站 {station_name} 坐标信息失败: {e}")

        return None
    
    def _calculate_cosine_similarity(self, vector1, vector2):
        """计算余弦相似度"""
        # 将字典转换为向量
        all_keys = set(vector1.keys()) | set(vector2.keys())
        
        v1 = np.array([vector1.get(key, 0) for key in all_keys])
        v2 = np.array([vector2.get(key, 0) for key in all_keys])
        
        # 计算余弦相似度
        dot_product = np.dot(v1, v2)
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)
        
        if norm1 == 0 or norm2 == 0:
            return 0
        
        return dot_product / (norm1 * norm2)
    
    def _predict_performance(self, similar_stations):
        """基于相似场站预测业绩"""
        if not similar_stations or self.poi_calculator.analyzer.order_analyzer is None:
            return None

        # 获取相似场站的业绩数据
        performance_data = []

        for station_info in similar_stations:
            station_name = station_info['name']
            similarity = station_info['similarity']

            if hasattr(self.poi_calculator.analyzer.order_analyzer, 'station_metrics'):
                metrics = self.poi_calculator.analyzer.order_analyzer.station_metrics
                if metrics is not None and station_name in metrics.index:
                    revenue = metrics.loc[station_name, 'total_revenue']
                    performance_data.append((revenue, similarity))
        
        if not performance_data:
            return None
        
        # 加权平均预测
        total_weight = sum(similarity for _, similarity in performance_data)
        if total_weight == 0:
            return None
        
        predicted_revenue = sum(revenue * similarity for revenue, similarity in performance_data) / total_weight
        
        # 转换为评分（简化处理）
        predicted_score = min(100, predicted_revenue / 1000)  # 假设1000元对应1分
        
        return {
            'predicted_revenue': predicted_revenue,
            'predicted_score': predicted_score,
            'reference_stations': len(performance_data)
        }
    
    def batch_evaluate_locations(self, locations):
        """批量评估多个位置
        
        参数:
            locations: 位置列表，每个元素为 (longitude, latitude, name)
        """
        results = []
        
        for i, location in enumerate(locations):
            if len(location) == 3:
                longitude, latitude, name = location
            else:
                longitude, latitude = location
                name = f"候选位置_{i+1}"
            
            print(f"\n评估进度: {i+1}/{len(locations)}")
            result = self.evaluate_new_location(longitude, latitude, name)
            if result:
                results.append(result)
            
            # 避免API请求过快
            time.sleep(1)
        
        return results
    
    def generate_evaluation_report(self, location_name, save_path=None):
        """生成评估报告"""
        if location_name not in self.evaluation_results:
            print(f"未找到位置 {location_name} 的评估结果")
            return None
        
        result = self.evaluation_results[location_name]
        
        if save_path is None:
            save_path = f"output/新场站评估_{result['longitude']}_{result['latitude']}.md"
        
        # 生成报告内容
        report = []
        report.append(f"# 新场站选址评估报告\n")
        report.append(f"## 基本信息\n")
        report.append(f"- **位置名称**: {result['location_name']}")
        report.append(f"- **坐标**: ({result['longitude']}, {result['latitude']})")
        report.append(f"- **评估时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"- **周围POI数量**: {result['poi_count']} 个\n")
        
        report.append(f"## 评分结果\n")
        report.append(f"| 评分类型 | 得分 | 说明 |")
        report.append(f"|----------|------|------|")
        report.append(f"| POI评分 | {result['poi_score']:.2f} | 基于周围POI的加权评分 |")
        report.append(f"| 战略价值评分 | {result['strategic_score']:.2f} | 品牌曝光和网络覆盖价值 |")
        report.append(f"| **综合评分** | **{result['combined_score']:.2f}** | **最终推荐指数** |\n")
        
        # 相似场站分析
        if result['similar_stations']:
            report.append(f"## 相似场站分析\n")
            report.append(f"基于POI特征找到的最相似现有场站：\n")
            report.append(f"| 排名 | 场站名称 | 相似度 | 坐标 | 地址 |")
            report.append(f"|------|----------|--------|------|------|")
            for i, station_info in enumerate(result['similar_stations'], 1):
                station_name = station_info['name']
                similarity = station_info['similarity']
                coords = station_info.get('coordinates')
                if coords:
                    coord_str = f"({coords['longitude']:.6f}, {coords['latitude']:.6f})"
                    address = coords.get('address', '未知')
                else:
                    coord_str = "未知"
                    address = "未知"
                report.append(f"| {i} | {station_name} | {similarity:.3f} | {coord_str} | {address} |")
            report.append("")
        
        # 业绩预测
        if result['predicted_performance']:
            pred = result['predicted_performance']
            report.append(f"## 业绩预测\n")
            report.append(f"基于 {pred['reference_stations']} 个相似场站的历史数据预测：")
            report.append(f"- **预测收益**: {pred['predicted_revenue']:.0f} 元")
            report.append(f"- **预测评分**: {pred['predicted_score']:.2f} 分\n")
        
        # POI分布分析
        report.append(f"## 周围POI分析\n")
        poi_types = {}
        for poi in result['pois']:
            poi_type = poi.get('type', '未知').split(';')[0]
            poi_types[poi_type] = poi_types.get(poi_type, 0) + 1
        
        report.append(f"主要POI类型分布：")
        for poi_type, count in sorted(poi_types.items(), key=lambda x: x[1], reverse=True):
            report.append(f"- {poi_type}: {count} 个")
        report.append("")
        
        # 建议
        report.append(f"## 选址建议\n")
        if result['combined_score'] >= 70:
            report.append(f"🟢 **强烈推荐**: 综合评分较高，建议优先考虑此位置")
        elif result['combined_score'] >= 50:
            report.append(f"🟡 **可以考虑**: 综合评分中等，可作为备选方案")
        else:
            report.append(f"🔴 **不推荐**: 综合评分较低，建议寻找其他位置")
        
        # 保存报告
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"评估报告已保存到: {save_path}")
        return save_path

    def create_evaluation_visualization(self, location_name):
        """为评估结果创建可视化图片"""
        if location_name not in self.evaluation_results:
            print(f"未找到位置 {location_name} 的评估结果")
            return None

        result = self.evaluation_results[location_name]

        # 创建综合可视化图片
        plt.figure(figsize=(16, 12))

        # 1. 评分雷达图
        plt.subplot(2, 3, 1)
        categories = ['POI评分', '战略价值', '综合评分']
        values = [result['poi_score'], result['strategic_score'], result['combined_score']]

        # 标准化到0-100范围
        max_score = max(values) if max(values) > 0 else 100
        normalized_values = [v/max_score*100 for v in values]

        angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]
        normalized_values += normalized_values[:1]

        plt.polar(angles, normalized_values, 'o-', linewidth=2, color='blue')
        plt.fill(angles, normalized_values, alpha=0.25, color='blue')
        plt.xticks(angles[:-1], categories)
        plt.ylim(0, 100)
        plt.title('评分雷达图')

        # 2. POI类型分布
        plt.subplot(2, 3, 2)
        poi_types = {}
        for poi in result['pois']:
            poi_type = poi.get('type', '未知').split(';')[0]
            poi_types[poi_type] = poi_types.get(poi_type, 0) + 1

        if poi_types:
            top_types = sorted(poi_types.items(), key=lambda x: x[1], reverse=True)[:8]
            types, counts = zip(*top_types)

            plt.pie(counts, labels=types, autopct='%1.1f%%', startangle=90)
            plt.title('周围POI类型分布')

        # 3. 距离分布
        plt.subplot(2, 3, 3)
        distances = [float(poi.get('distance', 0)) for poi in result['pois'] if poi.get('distance')]
        if distances:
            plt.hist(distances, bins=15, alpha=0.7, color='green', edgecolor='black')
            plt.xlabel('距离 (米)')
            plt.ylabel('POI数量')
            plt.title('POI距离分布')
            plt.axvline(x=np.mean(distances), color='red', linestyle='--',
                       label=f'平均距离: {np.mean(distances):.0f}m')
            plt.legend()

        # 4. 相似场站对比
        plt.subplot(2, 3, 4)
        if result['similar_stations']:
            stations = [station_info['name'] for station_info in result['similar_stations']]
            similarities = [station_info['similarity'] for station_info in result['similar_stations']]
            stations = [s[:15] + '...' if len(s) > 15 else s for s in stations]

            bars = plt.barh(range(len(stations)), similarities, color='orange', alpha=0.7)
            plt.yticks(range(len(stations)), stations)
            plt.xlabel('相似度')
            plt.title('最相似的现有场站')
            plt.gca().invert_yaxis()

            # 添加数值标签
            for i, bar in enumerate(bars):
                width = bar.get_width()
                plt.text(width + 0.01, bar.get_y() + bar.get_height()/2,
                        f'{width:.3f}', ha='left', va='center', fontsize=9)

        # 5. 评分对比
        plt.subplot(2, 3, 5)
        score_names = ['POI评分', '战略价值评分', '综合评分']
        score_values = [result['poi_score'], result['strategic_score'], result['combined_score']]
        colors = ['skyblue', 'orange', 'lightgreen']

        bars = plt.bar(score_names, score_values, color=colors, alpha=0.8)
        plt.ylabel('评分')
        plt.title('各维度评分')
        plt.xticks(rotation=45)

        # 添加数值标签
        for bar, value in zip(bars, score_values):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(score_values)*0.01,
                    f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

        # 6. 推荐等级
        plt.subplot(2, 3, 6)
        score = result['combined_score']
        if score >= 70:
            recommendation = "强烈推荐"
            color = 'green'
            emoji = "🟢"
        elif score >= 50:
            recommendation = "可以考虑"
            color = 'orange'
            emoji = "🟡"
        else:
            recommendation = "不推荐"
            color = 'red'
            emoji = "🔴"

        plt.text(0.5, 0.7, emoji, transform=plt.gca().transAxes,
                fontsize=60, ha='center', va='center')
        plt.text(0.5, 0.4, recommendation, transform=plt.gca().transAxes,
                fontsize=20, ha='center', va='center', color=color, fontweight='bold')
        plt.text(0.5, 0.2, f'综合评分: {score:.1f}', transform=plt.gca().transAxes,
                fontsize=16, ha='center', va='center')
        plt.axis('off')
        plt.title('推荐等级')

        plt.suptitle(f'新场站选址评估 - {result["location_name"]}', fontsize=16, fontweight='bold')
        plt.tight_layout()

        # 保存图片
        image_path = f"output/新场站评估_{result['longitude']}_{result['latitude']}.png"
        plt.savefig(image_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"评估可视化图片已保存到: {image_path}")
        return image_path

    def create_batch_comparison_visualization(self, results):
        """创建批量评估结果的对比可视化"""
        if not results:
            return None

        plt.figure(figsize=(16, 10))

        # 提取数据
        names = [r['location_name'] for r in results]
        poi_scores = [r['poi_score'] for r in results]
        strategic_scores = [r['strategic_score'] for r in results]
        combined_scores = [r['combined_score'] for r in results]
        poi_counts = [r['poi_count'] for r in results]

        # 1. 综合评分对比
        plt.subplot(2, 3, 1)
        sorted_results = sorted(results, key=lambda x: x['combined_score'], reverse=True)
        sorted_names = [r['location_name'] for r in sorted_results]
        sorted_scores = [r['combined_score'] for r in sorted_results]

        bars = plt.bar(range(len(sorted_names)), sorted_scores,
                      color=['green' if s >= 70 else 'orange' if s >= 50 else 'red' for s in sorted_scores],
                      alpha=0.8)
        plt.xticks(range(len(sorted_names)),
                  [name[:10] + '...' if len(name) > 10 else name for name in sorted_names],
                  rotation=45, ha='right')
        plt.ylabel('综合评分')
        plt.title('候选位置综合评分排名')

        # 添加数值标签
        for bar, score in zip(bars, sorted_scores):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
                    f'{score:.1f}', ha='center', va='bottom', fontweight='bold')

        # 2. 多维度评分对比
        plt.subplot(2, 3, 2)
        x = np.arange(len(names))
        width = 0.25

        plt.bar(x - width, poi_scores, width, label='POI评分', color='skyblue', alpha=0.8)
        plt.bar(x, strategic_scores, width, label='战略价值评分', color='orange', alpha=0.8)
        plt.bar(x + width, combined_scores, width, label='综合评分', color='lightgreen', alpha=0.8)

        plt.xticks(x, [name[:8] + '...' if len(name) > 8 else name for name in names],
                  rotation=45, ha='right')
        plt.ylabel('评分')
        plt.title('多维度评分对比')
        plt.legend()

        # 3. POI数量对比
        plt.subplot(2, 3, 3)
        plt.scatter(poi_counts, combined_scores, s=100, alpha=0.7, color='purple')
        plt.xlabel('周围POI数量')
        plt.ylabel('综合评分')
        plt.title('POI数量 vs 综合评分')

        # 添加标签
        for i, name in enumerate(names):
            plt.annotate(name[:8] + '...' if len(name) > 8 else name,
                        (poi_counts[i], combined_scores[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)

        # 4. 评分分布
        plt.subplot(2, 3, 4)
        plt.hist([poi_scores, strategic_scores, combined_scores],
                bins=10, alpha=0.7, label=['POI评分', '战略价值评分', '综合评分'])
        plt.xlabel('评分')
        plt.ylabel('位置数量')
        plt.title('评分分布')
        plt.legend()

        # 5. 推荐等级分布
        plt.subplot(2, 3, 5)
        recommendations = []
        for score in combined_scores:
            if score >= 70:
                recommendations.append('强烈推荐')
            elif score >= 50:
                recommendations.append('可以考虑')
            else:
                recommendations.append('不推荐')

        from collections import Counter
        rec_counts = Counter(recommendations)

        colors = {'强烈推荐': 'green', '可以考虑': 'orange', '不推荐': 'red'}
        plt.pie(rec_counts.values(), labels=rec_counts.keys(), autopct='%1.0f%%',
               colors=[colors[k] for k in rec_counts.keys()], startangle=90)
        plt.title('推荐等级分布')

        # 6. 排名表
        plt.subplot(2, 3, 6)
        plt.axis('off')

        # 创建排名表
        ranking_text = "排名表:\n\n"
        for i, result in enumerate(sorted_results, 1):
            ranking_text += f"{i}. {result['location_name'][:15]}\n"
            ranking_text += f"   综合评分: {result['combined_score']:.1f}\n\n"

        plt.text(0.1, 0.9, ranking_text, transform=plt.gca().transAxes,
                fontsize=10, va='top', ha='left',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.suptitle('新场站选址批量评估对比', fontsize=16, fontweight='bold')
        plt.tight_layout()

        # 保存图片
        image_path = f"output/批量场站评估对比_{len(results)}个位置.png"
        plt.savefig(image_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"批量评估对比图已保存到: {image_path}")
        return image_path

def main():
    """演示新场站选址评估"""
    print("新场站选址评估演示")
    print("=" * 40)
    
    # 创建评估器
    evaluator = NewStationEvaluator(use_distance_weight=True)
    
    # 示例坐标（北京市中心附近）
    test_locations = [
        (116.397452, 39.909187, "天安门广场附近"),
        (116.407526, 39.90403, "王府井附近"),
        (121.473701, 31.230416, "上海外滩附近")
    ]
    
    # 批量评估
    results = evaluator.batch_evaluate_locations(test_locations)
    
    # 生成报告和可视化
    for result in results:
        # 生成文字报告
        evaluator.generate_evaluation_report(result['location_name'])

        # 生成可视化图片
        evaluator.create_evaluation_visualization(result['location_name'])

    # 生成批量对比可视化
    if len(results) > 1:
        evaluator.create_batch_comparison_visualization(results)

        print(f"\n对比分析:")
        print(f"{'位置名称':<20} {'POI评分':<10} {'战略评分':<10} {'综合评分':<10}")
        print("-" * 60)
        for result in sorted(results, key=lambda x: x['combined_score'], reverse=True):
            print(f"{result['location_name']:<20} {result['poi_score']:<10.2f} {result['strategic_score']:<10.2f} {result['combined_score']:<10.2f}")

    print("\n新场站选址评估演示完成！")
    print("\n生成的文件:")
    print("- 文字报告: output/新场站评估_*.md")
    print("- 可视化图片: output/新场站评估_*.png")
    if len(results) > 1:
        print("- 批量对比图: output/批量场站评估对比_*.png")

if __name__ == "__main__":
    main()
