#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电站评估报告模板

此模块定义了充电站评估报告的各部分模板
"""

# 评估概览模板
OVERVIEW_TEMPLATE = """
<h2>评估概览</h2>
<p><strong>评估坐标:</strong> ({longitude}, {latitude})</p>
<p><strong>POI评分:</strong> {poi_score:.2f}</p>
<p><strong>战略价值评分:</strong> {strategic_score:.2f}</p>
<p><strong>业绩评分:</strong> {performance_score:.2f}</p>
<p><strong>综合评分:</strong> {combined_score:.2f}</p>
<p><strong>预测周营业额:</strong> {predicted_revenue:.2f}元</p>
<p><strong>建议:</strong> {recommendation}建设充电站</p>
"""

# POI评分解析模板
POI_ANALYSIS_TEMPLATE = """
<h3>POI评分解析</h3>
<p>POI评分反映了该位置周边的兴趣点分布情况，包括商业设施、交通枢纽、住宅区等。高分表示该位置周边POI丰富，有利于充电站的客流量。</p>
<p>当前位置POI评分为 <strong>{poi_score:.2f}</strong>，属于<strong>{assessment}</strong>水平。</p>
"""

# 战略价值评分解析模板
STRATEGIC_ANALYSIS_TEMPLATE = """
<h3>战略价值评分解析</h3>
<p>战略价值评分考虑了品牌曝光度和网络覆盖价值两个方面。高分表示该位置对品牌曝光和网络覆盖有较高的战略价值。</p>
<p>当前位置战略价值评分为 <strong>{strategic_score:.2f}</strong>，战略价值<strong>{assessment}</strong>。</p>
"""

# 业绩评分解析模板
PERFORMANCE_ANALYSIS_TEMPLATE = """
<h3>业绩评分解析</h3>
<p>业绩评分基于历史数据和相似场站的表现预测该位置的业绩表现。高分表示该位置预期有较好的业绩表现。</p>
<p>当前位置业绩评分为 <strong>{performance_score:.2f}</strong>，预期业绩表现<strong>{assessment}</strong>。</p>
"""

# 综合评分解析模板
COMBINED_ANALYSIS_TEMPLATE = """
<h3>综合评分解析</h3>
<p>综合评分综合考虑了POI评分、业绩评分和战略价值评分，全面反映该位置的充电站适合度。</p>
<p>当前位置综合评分为 <strong>{combined_score:.2f}</strong>，综合适合度<strong>{assessment}</strong>。</p>
"""

# 相似场站分析模板
SIMILAR_STATIONS_TEMPLATE = """
<h2>相似场站分析</h2>
<p>以下是与当前位置最相似的充电站，可作为参考：</p>
<table>
    <tr>
        <th>场站名称</th>
        <th>相似度</th>
        <th>POI评分</th>
        <th>业绩评分</th>
    </tr>
    <!-- 这里将插入相似场站数据 -->
</table>
<p>通过分析相似场站的表现，可以更好地预测当前位置的潜力和可能面临的挑战。</p>
"""

# 建议与结论模板
RECOMMENDATION_TEMPLATE = """
<h2>建议与结论</h2>
<p>该位置<strong>{recommendation}</strong>建设充电站，具有以下特点：</p>
<ul>
    <li>周边POI分布{poi_assessment}，客流量潜力{poi_potential}</li>
    <li>战略价值{strategic_assessment}，对品牌曝光和网络覆盖作用{strategic_impact}</li>
    <li>预期业绩表现{performance_assessment}</li>
</ul>
<p>建议{action_suggestion}</p>
"""

# 市场分析模板
MARKET_ANALYSIS_TEMPLATE = """
<h2>市场分析</h2>
<p>根据评估数据，对该位置的市场环境进行分析：</p>
<h3>需求分析</h3>
<p>该区域的充电需求主要来源于...</p>
<h3>竞争分析</h3>
<p>该区域的竞争情况...</p>
<h3>增长潜力</h3>
<p>未来发展潜力...</p>
"""

# 风险评估模板
RISK_ASSESSMENT_TEMPLATE = """
<h2>风险评估</h2>
<p>在该位置建设充电站可能面临的主要风险：</p>
<ul>
    <li><strong>市场风险：</strong>...</li>
    <li><strong>运营风险：</strong>...</li>
    <li><strong>政策风险：</strong>...</li>
</ul>
<p>风险缓解建议：...</p>
"""

# 投资回报分析模板
ROI_ANALYSIS_TEMPLATE = """
<h2>投资回报分析</h2>
<p>基于预测周营业额{predicted_revenue:.2f}元，估算投资回报情况：</p>
<h3>投资估算</h3>
<p>初期投资约...</p>
<h3>收益预测</h3>
<p>年收益约...</p>
<h3>回收期</h3>
<p>预计投资回收期约...</p>
"""

# 获取所有模板
def get_all_templates():
    """获取所有报告模板"""
    return {
        "概览": OVERVIEW_TEMPLATE,
        "POI分析": POI_ANALYSIS_TEMPLATE,
        "战略价值分析": STRATEGIC_ANALYSIS_TEMPLATE,
        "业绩分析": PERFORMANCE_ANALYSIS_TEMPLATE,
        "综合评分分析": COMBINED_ANALYSIS_TEMPLATE,
        "相似场站分析": SIMILAR_STATIONS_TEMPLATE,
        "建议与结论": RECOMMENDATION_TEMPLATE,
        "市场分析": MARKET_ANALYSIS_TEMPLATE,
        "风险评估": RISK_ASSESSMENT_TEMPLATE,
        "投资回报分析": ROI_ANALYSIS_TEMPLATE
    }

# 获取基础模板（不使用LLM时的默认模板）
def get_basic_templates():
    """获取基础报告模板"""
    return {
        "概览": OVERVIEW_TEMPLATE,
        "POI分析": POI_ANALYSIS_TEMPLATE,
        "战略价值分析": STRATEGIC_ANALYSIS_TEMPLATE,
        "业绩分析": PERFORMANCE_ANALYSIS_TEMPLATE,
        "综合评分分析": COMBINED_ANALYSIS_TEMPLATE,
        "相似场站分析": SIMILAR_STATIONS_TEMPLATE,
        "建议与结论": RECOMMENDATION_TEMPLATE
    }
