#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交互式新场站分析报告生成工具

此脚本提供交互式界面，让用户可以方便地为新场站生成分析报告。
包含预设的示例坐标和完整的分析流程。

使用方法：
python interactive_new_station_report.py
"""

import os
import sys
from new_station_report_generator import generate_new_station_analysis_report

def show_welcome():
    """显示欢迎信息"""
    print("=" * 70)
    print("🚗 新场站分析报告生成工具 🚗")
    print("=" * 70)
    print("此工具将为您的新充电站选址提供全面的分析报告，包括：")
    print("• POI评分分析")
    print("• 战略价值评估")
    print("• 业绩预测")
    print("• 可视化图表")
    print("• LLM增强的详细报告")
    print("=" * 70)

def show_sample_locations():
    """显示示例位置"""
    print("\n📍 示例位置（可直接选择）：")
    print("1. 北京天安门广场 (116.407526, 39.90403)")
    print("2. 上海外滩 (121.473701, 31.230416)")
    print("3. 北京国贸 (116.397452, 39.909187)")
    print("4. 自定义坐标")

def get_user_choice():
    """获取用户选择"""
    while True:
        try:
            choice = input("\n请选择位置 (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                return int(choice)
            else:
                print("请输入有效的选择 (1-4)")
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            sys.exit(0)
        except:
            print("请输入有效的选择 (1-4)")

def get_coordinates_from_choice(choice):
    """根据选择获取坐标"""
    if choice == 1:
        return 116.407526, 39.90403, "北京天安门广场"
    elif choice == 2:
        return 121.473701, 31.230416, "上海外滩"
    elif choice == 3:
        return 116.397452, 39.909187, "北京国贸"
    elif choice == 4:
        return get_custom_coordinates()

def get_custom_coordinates():
    """获取自定义坐标"""
    print("\n📍 请输入自定义坐标：")
    while True:
        try:
            longitude = float(input("经度: ").strip())
            latitude = float(input("纬度: ").strip())
            location_name = input("位置名称（可选）: ").strip() or f"自定义位置({longitude}, {latitude})"
            
            # 简单的坐标范围检查
            if not (-180 <= longitude <= 180):
                print("经度应在 -180 到 180 之间")
                continue
            if not (-90 <= latitude <= 90):
                print("纬度应在 -90 到 90 之间")
                continue
                
            return longitude, latitude, location_name
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            sys.exit(0)

def get_analysis_options():
    """获取分析选项"""
    print("\n⚙️ 分析选项：")
    
    # 搜索半径
    while True:
        try:
            radius_input = input("POI搜索半径（米，默认1000）: ").strip()
            radius = int(radius_input) if radius_input else 1000
            if radius <= 0:
                print("搜索半径必须大于0")
                continue
            break
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            sys.exit(0)
    
    # LLM选项
    while True:
        try:
            llm_input = input("使用LLM生成详细报告？(y/n，默认y): ").strip().lower()
            use_llm = llm_input != 'n' if llm_input else True
            break
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            sys.exit(0)
    
    return radius, use_llm

def main():
    """主函数"""
    try:
        # 显示欢迎信息
        show_welcome()
        
        # 显示示例位置
        show_sample_locations()
        
        # 获取用户选择
        choice = get_user_choice()
        
        # 获取坐标
        longitude, latitude, location_name = get_coordinates_from_choice(choice)
        
        # 获取分析选项
        radius, use_llm = get_analysis_options()
        
        # 确认信息
        print("\n📋 分析配置确认：")
        print(f"位置: {location_name}")
        print(f"坐标: ({longitude}, {latitude})")
        print(f"搜索半径: {radius}米")
        print(f"使用LLM: {'是' if use_llm else '否'}")
        
        confirm = input("\n确认开始分析？(y/n): ").strip().lower()
        if confirm == 'n':
            print("分析已取消")
            return
        
        # 生成报告
        print("\n🚀 开始生成分析报告...")
        report_file, image_file = generate_new_station_analysis_report(
            longitude=longitude,
            latitude=latitude,
            radius=radius,
            use_llm=use_llm,
            template_type='basic',
            auto_open_browser=True
        )
        
        if report_file:
            print(f"\n🎉 {location_name} 的分析报告生成完成！")
            print("\n📁 生成的文件：")
            print(f"  📊 评估图片: {image_file}")
            print(f"  📄 分析报告: {report_file}")
            print("\n💡 提示：报告已在浏览器中自动打开")
            
            # 询问是否继续分析其他位置
            while True:
                try:
                    continue_input = input("\n是否分析其他位置？(y/n): ").strip().lower()
                    if continue_input == 'y':
                        print("\n" + "="*70)
                        main()  # 递归调用
                        return
                    elif continue_input == 'n':
                        print("\n👋 感谢使用新场站分析工具！")
                        return
                    else:
                        print("请输入 y 或 n")
                except KeyboardInterrupt:
                    print("\n\n👋 再见！")
                    return
        else:
            print(f"\n❌ {location_name} 的报告生成失败")
            print("请检查网络连接和配置设置")
            
    except KeyboardInterrupt:
        print("\n\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
