#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重命名POI数据文件，使用场站名称作为文件名
"""

import os
import pandas as pd
import re
import shutil

def rename_poi_files():
    """重命名POI数据文件，使用场站名称作为文件名"""
    print("开始重命名POI数据文件...")

    # 加载场站数据
    station_data_file = 'resource/final_stations_107.csv'
    if not os.path.exists(station_data_file):
        print(f"场站数据文件 {station_data_file} 不存在")
        return

    station_data = pd.read_csv(station_data_file)

    # 创建坐标到场站名称的映射
    coord_to_name = {}
    for _, row in station_data.iterrows():
        # 使用高德坐标
        if '高德_经度' in station_data.columns and '高德_纬度' in station_data.columns:
            lon = row['高德_经度']
            lat = row['高德_纬度']
        # 使用GCJ02坐标
        elif 'gcj02_lon' in station_data.columns and 'gcj02_lat' in station_data.columns:
            lon = row['gcj02_lon']
            lat = row['gcj02_lat']
        # 使用普通坐标
        else:
            lon = row['经度']
            lat = row['纬度']

        # 将坐标转换为字符串，保留6位小数
        coord_key = f"{lon:.6f}_{lat:.6f}"
        coord_to_name[coord_key] = row['名称']


    # 创建备份目录
    backup_dir = 'poi_data_backup'
    os.makedirs(backup_dir, exist_ok=True)

    # 遍历POI数据目录中的文件
    poi_dir = 'poi_data'
    renamed_count = 0
    skipped_count = 0

    for filename in os.listdir(poi_dir):
        if filename.endswith('.json') and filename.startswith('poi_data_'):
            # 从文件名中提取坐标
            match = filename.replace('.json', '')
            if match:
                lon = float(match.group(1))
                lat = float(match.group(2))
                timestamp = match.group(3)

                # 将坐标转换为字符串，保留6位小数
                file_coord_key = f"{lon:.6f}_{lat:.6f}"

                station_name = coord_to_name[file_coord_key]

                # 查找最接近的坐标
                # closest_key = None
                # min_distance = float('inf')

                # for key in coord_to_name.keys():
                #     key_lon, key_lat = map(float, key.split('_'))
                #     distance = ((lon - key_lon) ** 2 + (lat - key_lat) ** 2) ** 0.5
                #     if distance < min_distance:
                #         min_distance = distance
                #         closest_key = key

                # 如果找到了匹配的场站，重命名文件
                #if closest_key and min_distance < 0.001:  # 距离阈值
                #    station_name = coord_to_name[closest_key]
                if station_name:
                    new_filename = f"{station_name}.json"

                    # 检查新文件名是否已存在
                    if os.path.exists(os.path.join(poi_dir, new_filename)):
                        # 如果已存在，添加时间戳
                        new_filename = f"{station_name}_{timestamp}.json"

                    # 备份原文件
                    shutil.copy2(os.path.join(poi_dir, filename), os.path.join(backup_dir, filename))

                    # 重命名文件
                    os.rename(os.path.join(poi_dir, filename), os.path.join(poi_dir, new_filename))
                    print(f"已重命名: {filename} -> {new_filename}")
                    renamed_count += 1
                else:
                    print(f"未找到匹配的场站: {filename}")
                    skipped_count += 1
            else:
                print(f"文件名格式不匹配: {filename}")
                skipped_count += 1

    print(f"\n重命名完成: {renamed_count} 个文件已重命名, {skipped_count} 个文件已跳过")
    print(f"原文件已备份到 {backup_dir} 目录")

def rename_poi_files_withCoord():
    print("开始重命名POI数据文件，以场站名和坐标为文件名...")

    # 加载场站数据
    station_data_file = 'resource/final_stations_107.csv'
    if not os.path.exists(station_data_file):
        print(f"场站数据文件 {station_data_file} 不存在")
        return

    station_data = pd.read_csv(station_data_file)

    # 创建坐标到场站名称的映射
    name_with_coord = {}
    for _, row in station_data.iterrows():
        # 使用高德坐标
        if '高德_经度' in station_data.columns and '高德_纬度' in station_data.columns:
            lon = row['高德_经度']
            lat = row['高德_纬度']
        # 使用GCJ02坐标
        elif 'gcj02_lon' in station_data.columns and 'gcj02_lat' in station_data.columns:
            lon = row['gcj02_lon']
            lat = row['gcj02_lat']
        # 使用普通坐标
        else:
            lon = row['经度']
            lat = row['纬度']

        # 将坐标转换为字符串，保留6位小数
        name_key = row['名称']
        name_coord_value = row['名称'] + f"_{lon:.6f}_{lat:.6f}"
        name_with_coord[name_key] = name_coord_value

    # 创建备份目录
    backup_dir = 'poi_data_backup'
    os.makedirs(backup_dir, exist_ok=True)

    # 遍历POI数据目录中的文件
    poi_dir = 'poi_data'
    renamed_count = 0
    skipped_count = 0

    for filename in os.listdir(poi_dir):
        if filename.endswith('.json'): #and filename.startswith('poi_data_'):
            # 从文件名中提取坐标
            match = filename.replace(".json", "")
            if match:
                station_name = name_with_coord[match]
                if station_name:
                    new_filename = f"{station_name}.json"

                    # 检查新文件名是否已存在
                    # if os.path.exists(os.path.join(poi_dir, new_filename)):
                    #     # 如果已存在，添加时间戳
                    #     new_filename = f"{station_name}_{timestamp}.json"

                    # 备份原文件
                    shutil.copy2(os.path.join(poi_dir, filename), os.path.join(backup_dir, filename))

                    # 重命名文件
                    os.rename(os.path.join(poi_dir, filename), os.path.join(poi_dir, new_filename))
                    print(f"已重命名: {filename} -> {new_filename}")
                    renamed_count += 1
                else:
                    print(f"未找到匹配的场站: {filename}")
                    skipped_count += 1
            else:
                print(f"文件名格式不匹配: {filename}")
                skipped_count += 1

    print(f"\n重命名完成: {renamed_count} 个文件已重命名, {skipped_count} 个文件已跳过")
    print(f"原文件已备份到 {backup_dir} 目录")


if __name__ == "__main__":
    #rename_poi_files()
    rename_poi_files_withCoord()
