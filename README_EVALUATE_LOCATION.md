# 充电站新位置评估工具

这个工具用于评估给定坐标位置建设充电站的适合度，通过分析周边POI数据、预测业绩表现，并给出综合评分和建议。

## 功能特点

1. **POI数据获取**：使用高德地图API获取指定坐标周边的POI数据
2. **POI评分计算**：基于优化后的POI权重计算POI评分
3. **业绩预测**：预测新充电站的周营业额和业绩评分
4. **综合评分**：结合POI评分和业绩评分，给出综合评分
5. **相似场站分析**：找出与新位置最相似的现有充电站
6. **可视化结果**：生成评估结果可视化图表
7. **建议生成**：根据综合评分给出建设建议

## 使用方法

### 命令行模式

```bash
python evaluate_new_location.py --longitude 121.473701 --latitude 31.230416 [--radius 1000]
```

参数说明：
- `--longitude`：经度（必需）
- `--latitude`：纬度（必需）
- `--radius`：搜索半径，单位米，默认1000米
- `--api_key`：高德地图API密钥，默认使用系统配置的密钥
- `--order_data`：订单数据文件路径，默认使用`resource/2025_Q2_10cols_107stations.csv`

### 交互模式

```bash
python evaluate_new_location.py --interactive
```

在交互模式下，程序会引导您输入经度、纬度和搜索半径，然后进行评估。您可以连续评估多个位置，无需重新启动程序。

## 评估结果说明

评估结果包括以下内容：

1. **POI评分**：基于周边POI数据计算的评分，范围0-100
2. **业绩评分**：预测的业绩表现评分，范围0-100
3. **综合评分**：POI评分和业绩评分的加权平均，范围0-100
4. **预测周营业额**：预测的每周营业额，单位元
5. **建议**：根据综合评分给出的建设建议
   - 非常适合：综合评分 >= 40
   - 适合：综合评分 >= 30
   - 一般：综合评分 >= 20
   - 不太适合：综合评分 < 20
6. **最相似的场站**：与新位置最相似的现有充电站及其评分

## 输出文件

1. **POI数据**：保存在`raw_poi_data/新坐标_{longitude}_{latitude}_{timestamp}.json`
2. **评估结果可视化**：保存在`output/坐标评估_{longitude}_{latitude}.png`

## 示例

```bash
python evaluate_new_location.py --longitude 121.473701 --latitude 31.230416
```

输出示例：
```
评估结果:
坐标: (121.473701, 31.230416)
POI评分: 35.67
业绩评分: 78.92
综合评分: 52.57
预测周营业额: 8765.32元
建议: 非常适合建设充电站

最相似的场站:
- 上海国际旅游度假区充电站: 评分 87.65
- 上海市徐汇区漕河泾开发区充电站: 评分 82.34
- 上海市浦东新区张江高科技园区充电站: 评分 79.21
- 上海市静安区南京西路充电站: 评分 75.89
- 上海市长宁区中山公园充电站: 评分 72.45
```

## 依赖项

- Python 3.6+
- pandas
- numpy
- matplotlib
- scikit-learn
- requests

## 注意事项

1. 确保您有有效的高德地图API密钥
2. 确保`resource`目录下有有效的订单数据文件
3. 评估结果的准确性取决于训练数据的质量和数量
4. 对于POI数据稀疏的区域，评估结果可能不够准确
