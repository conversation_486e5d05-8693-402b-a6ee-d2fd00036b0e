#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强的POI计算器
实现更有效的POI计算方法，提升计算准确性和效率
"""

import os
import json
import pandas as pd
import numpy as np
import math
from collections import defaultdict
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

class EnhancedPOICalculator:
    """增强的POI计算器类"""
    
    def __init__(self, config_file='poi_config.json'):
        """初始化计算器
        
        参数:
            config_file: 配置文件路径
        """
        self.config = self._load_config(config_file)
        self.poi_vectors = None
        self.weights = None
        self.feature_importance = None
        self.ml_model = None
        
    def _load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            'distance_weight': {
                'enabled': True,
                'decay_factor': 1000.0,
                'max_distance': 3000,
                'min_weight': 0.1
            },
            'quality_weight': {
                'enabled': True,
                'name_factor': 0.2,
                'type_factor': 0.1,
                'address_factor': 0.1
            },
            'density_features': {
                'enabled': True,
                'radii': [500, 1000, 2000],
                'categories': ['05', '06', '08', '12', '15']
            },
            'base_weights': {
                '150100': 2.5,  # 公交车站
                '150200': 2.0,  # 地铁站
                '120200': 1.8,  # 住宅区
                '050000': 1.5,  # 餐饮服务
                '060100': 1.5,  # 商场
                '011100': 3.0,  # 充电站
                '010100': 2.5,  # 加油站
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        return default_config
    
    def calculate_distance_weight(self, distance):
        """计算距离权重
        
        参数:
            distance: 距离（米）
            
        返回:
            距离权重 (0.1-1.0)
        """
        if not self.config['distance_weight']['enabled']:
            return 1.0
            
        if distance > self.config['distance_weight']['max_distance']:
            return self.config['distance_weight']['min_weight']
        
        # 使用指数衰减函数
        decay_factor = self.config['distance_weight']['decay_factor']
        weight = np.exp(-distance / decay_factor)
        return max(self.config['distance_weight']['min_weight'], weight)
    
    def calculate_quality_weight(self, poi):
        """计算POI质量权重
        
        参数:
            poi: POI数据字典
            
        返回:
            质量权重 (0.5-2.0)
        """
        if not self.config['quality_weight']['enabled']:
            return 1.0
            
        weight = 1.0
        
        # 名称长度影响
        name = poi.get('name', '')
        if len(name) > 10:
            weight += self.config['quality_weight']['name_factor']
        elif len(name) < 5:
            weight -= self.config['quality_weight']['name_factor']
        
        # 类型详细程度影响
        poi_type = poi.get('type', '')
        type_levels = poi_type.count(';')
        if type_levels >= 2:
            weight += self.config['quality_weight']['type_factor']
        
        # 地址重要性影响
        address = poi.get('address', '')
        if any(keyword in address for keyword in ['路', '街', '大道', '广场']):
            weight += self.config['quality_weight']['address_factor']
        
        return max(0.5, min(2.0, weight))
    
    def calculate_density_features(self, pois):
        """计算密度和多样性特征
        
        参数:
            pois: POI列表
            
        返回:
            密度特征字典
        """
        if not self.config['density_features']['enabled']:
            return {}
            
        features = {}
        
        # 计算不同半径内的POI密度
        for radius in self.config['density_features']['radii']:
            pois_in_radius = [poi for poi in pois 
                            if float(poi.get('distance', 0)) <= radius]
            features[f'density_{radius}m'] = len(pois_in_radius)
        
        # 计算POI多样性指数（Shannon多样性指数）
        typecode_counts = defaultdict(int)
        for poi in pois:
            typecode = poi.get('typecode', '')
            if typecode and len(typecode) >= 2:
                large_category = typecode[:2]
                typecode_counts[large_category] += 1
        
        total_pois = sum(typecode_counts.values())
        if total_pois > 0:
            diversity_index = 0
            for count in typecode_counts.values():
                if count > 0:
                    p = count / total_pois
                    diversity_index -= p * np.log(p)
            features['diversity_index'] = diversity_index
        else:
            features['diversity_index'] = 0
        
        # 计算特定类别密度
        categories = self.config['density_features']['categories']
        for category in categories:
            count = sum(1 for poi in pois 
                       if poi.get('typecode', '').startswith(category))
            features[f'category_{category}_density'] = count
        
        return features
    
    def build_enhanced_poi_vectors(self, raw_poi_data, used_poi_codes):
        """构建增强的POI向量
        
        参数:
            raw_poi_data: 原始POI数据字典
            used_poi_codes: 使用的POI编码列表
            
        返回:
            增强的POI向量DataFrame
        """
        print("正在构建增强的POI向量...")
        
        station_vectors = {}
        
        for station_name, station_info in raw_poi_data.items():
            vector = defaultdict(float)
            pois = station_info['data'].get('pois', [])
            
            # 处理每个POI
            for poi in pois:
                typecode = poi.get('typecode', '')
                if not typecode or len(typecode) != 6:
                    continue
                
                # 计算权重
                distance = float(poi.get('distance', 0))
                distance_weight = self.calculate_distance_weight(distance)
                quality_weight = self.calculate_quality_weight(poi)
                final_weight = distance_weight * quality_weight
                
                # 避免重复计算，优先使用最具体的编码
                large_category = typecode[:2] + '0000'
                medium_category = typecode[:4] + '00'
                
                if typecode in used_poi_codes:
                    vector[typecode] += final_weight
                elif medium_category in used_poi_codes:
                    vector[medium_category] += final_weight
                elif large_category in used_poi_codes:
                    vector[large_category] += final_weight
            
            # 添加密度特征
            density_features = self.calculate_density_features(pois)
            vector.update(density_features)
            
            station_vectors[station_name] = dict(vector)
        
        # 创建DataFrame
        all_features = set()
        for vector in station_vectors.values():
            all_features.update(vector.keys())
        
        all_features = sorted(list(all_features))
        stations = sorted(station_vectors.keys())
        
        poi_vectors = pd.DataFrame(0.0, index=stations, columns=all_features)
        
        for station in stations:
            for feature, value in station_vectors[station].items():
                if feature in all_features:
                    poi_vectors.at[station, feature] = value
        
        self.poi_vectors = poi_vectors
        print(f"已构建 {len(stations)} 个站点的POI向量，包含 {len(all_features)} 个特征")
        
        return poi_vectors
    
    def optimize_weights_with_performance(self, performance_data):
        """使用业绩数据优化权重
        
        参数:
            performance_data: 业绩数据DataFrame，index为站点名，包含revenue列
            
        返回:
            优化后的权重字典
        """
        if self.poi_vectors is None:
            print("请先构建POI向量")
            return None
        
        print("正在使用业绩数据优化权重...")
        
        # 找到共同站点
        poi_stations = set(self.poi_vectors.index)
        perf_stations = set(performance_data.index)
        common_stations = list(poi_stations & perf_stations)
        
        if len(common_stations) < 10:
            print(f"共同站点数量太少（{len(common_stations)}），无法优化权重")
            return None
        
        # 准备训练数据
        X = self.poi_vectors.loc[common_stations].values
        y = performance_data.loc[common_stations, 'revenue'].values
        
        # 训练随机森林模型
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        # 评估模型
        cv_scores = cross_val_score(rf, X, y, cv=5, scoring='r2')
        print(f"模型交叉验证R²得分: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
        
        # 获取特征重要性
        feature_importance = pd.DataFrame({
            'feature': self.poi_vectors.columns,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        # 转换为权重
        min_imp = feature_importance['importance'].min()
        max_imp = feature_importance['importance'].max()
        
        optimized_weights = {}
        if max_imp > min_imp:
            for _, row in feature_importance.iterrows():
                importance = row['importance']
                weight = 0.1 + 2.9 * (importance - min_imp) / (max_imp - min_imp)
                optimized_weights[row['feature']] = weight
        
        self.weights = optimized_weights
        self.feature_importance = feature_importance
        self.ml_model = rf
        
        # 保存结果
        self._save_optimization_results(optimized_weights, feature_importance)
        
        print(f"已优化 {len(optimized_weights)} 个特征权重")
        return optimized_weights
    
    def _save_optimization_results(self, weights, feature_importance):
        """保存优化结果"""
        os.makedirs('output', exist_ok=True)
        
        # 保存权重
        with open('output/enhanced_poi_weights.json', 'w', encoding='utf-8') as f:
            json.dump(weights, f, ensure_ascii=False, indent=4)
        
        # 保存特征重要性
        feature_importance.to_csv('output/enhanced_feature_importance.csv', index=False)
        
        # 可视化特征重要性
        self._plot_feature_importance(feature_importance)
    
    def _plot_feature_importance(self, feature_importance, top_n=20):
        """绘制特征重要性图"""
        top_features = feature_importance.head(top_n)
        
        plt.figure(figsize=(12, 8))
        bars = plt.barh(range(len(top_features)), top_features['importance'])
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('特征重要性')
        plt.title(f'增强POI特征重要性（Top {top_n}）')
        plt.gca().invert_yaxis()
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            plt.text(width + 0.001, bar.get_y() + bar.get_height()/2, 
                    f'{width:.3f}', ha='left', va='center')
        
        plt.tight_layout()
        plt.savefig('output/enhanced_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("特征重要性图已保存到 output/enhanced_feature_importance.png")
    
    def calculate_station_score(self, station_vector, weights=None):
        """计算站点评分
        
        参数:
            station_vector: 站点POI向量
            weights: 权重字典，如果为None则使用优化后的权重
            
        返回:
            站点评分
        """
        if weights is None:
            weights = self.weights or self.config['base_weights']
        
        score = 0.0
        for feature, count in station_vector.items():
            if feature in weights:
                score += count * weights[feature]
            else:
                # 尝试使用层次权重
                if len(feature) == 6:  # POI编码
                    large_cat = feature[:2] + '0000'
                    medium_cat = feature[:4] + '00'
                    weight = weights.get(feature, 
                            weights.get(medium_cat,
                            weights.get(large_cat, 0.5)))
                else:
                    weight = 0.5  # 默认权重
                score += count * weight
        
        return score
    
    def compare_with_traditional_method(self, traditional_vectors):
        """与传统方法对比
        
        参数:
            traditional_vectors: 传统方法的POI向量
            
        返回:
            对比结果字典
        """
        if self.poi_vectors is None:
            print("请先构建增强POI向量")
            return None
        
        print("正在对比传统方法和增强方法...")
        
        # 计算评分
        traditional_scores = []
        enhanced_scores = []
        
        base_weights = self.config['base_weights']
        
        for station in self.poi_vectors.index:
            if station in traditional_vectors.index:
                # 传统方法评分
                trad_score = self.calculate_station_score(
                    traditional_vectors.loc[station], base_weights)
                traditional_scores.append(trad_score)
                
                # 增强方法评分
                enh_score = self.calculate_station_score(
                    self.poi_vectors.loc[station], self.weights or base_weights)
                enhanced_scores.append(enh_score)
        
        # 计算统计指标
        trad_stats = pd.Series(traditional_scores).describe()
        enh_stats = pd.Series(enhanced_scores).describe()
        
        # 计算相关性
        correlation = pd.Series(traditional_scores).corr(pd.Series(enhanced_scores))
        
        comparison = {
            'traditional_stats': trad_stats,
            'enhanced_stats': enh_stats,
            'correlation': correlation,
            'improvement': {
                'mean_change': (enh_stats['mean'] - trad_stats['mean']) / trad_stats['mean'] * 100,
                'std_change': (enh_stats['std'] - trad_stats['std']) / trad_stats['std'] * 100
            }
        }
        
        print(f"评分相关性: {correlation:.3f}")
        print(f"平均分变化: {comparison['improvement']['mean_change']:.1f}%")
        print(f"标准差变化: {comparison['improvement']['std_change']:.1f}%")
        
        return comparison
    
    def save_config(self, filepath='output/enhanced_poi_config.json'):
        """保存当前配置"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)
        print(f"配置已保存到 {filepath}")

if __name__ == "__main__":
    # 创建计算器实例
    calculator = EnhancedPOICalculator()
    
    # 保存默认配置
    calculator.save_config()
    print("增强POI计算器已初始化，配置已保存")
