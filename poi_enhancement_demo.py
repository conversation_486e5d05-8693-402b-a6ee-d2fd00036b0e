#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POI计算有效性提升演示脚本
展示改进前后的POI计算效果对比
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from station_analysis import StationAnalyzer
import time

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def compare_poi_calculation_methods():
    """对比不同POI计算方法的效果"""
    print("POI计算有效性提升演示")
    print("=" * 50)
    
    # 初始化分析器
    analyzer = StationAnalyzer()
    analyzer.load_data()
    
    print("\n1. 传统POI向量构建方法")
    print("-" * 30)
    start_time = time.time()
    
    # 传统方法：不使用距离权重和增强特征
    traditional_vectors = analyzer.build_poi_vectors(
        use_distance_weight=False, 
        use_enhanced_features=False
    )
    traditional_time = time.time() - start_time
    print(f"传统方法耗时: {traditional_time:.2f}秒")
    print(f"特征数量: {traditional_vectors.shape[1]}")
    
    # 计算传统方法的评分
    analyzer.build_scoring_framework(calculate_strategic=False)
    traditional_scores = analyzer.scores_df.copy()
    
    print("\n2. 增强POI向量构建方法")
    print("-" * 30)
    start_time = time.time()
    
    # 增强方法：使用距离权重和增强特征
    enhanced_vectors = analyzer.build_poi_vectors(
        use_distance_weight=True, 
        use_enhanced_features=True
    )
    enhanced_time = time.time() - start_time
    print(f"增强方法耗时: {enhanced_time:.2f}秒")
    print(f"特征数量: {enhanced_vectors.shape[1]}")
    
    # 计算增强方法的评分
    analyzer.build_scoring_framework(calculate_strategic=False)
    enhanced_scores = analyzer.scores_df.copy()
    
    print("\n3. 机器学习权重优化")
    print("-" * 30)
    if analyzer.order_analyzer is not None:
        start_time = time.time()
        optimized_weights = analyzer.optimize_poi_weights_with_ml()
        ml_time = time.time() - start_time
        print(f"机器学习优化耗时: {ml_time:.2f}秒")
        
        if optimized_weights:
            # 重新计算使用优化权重的评分
            analyzer.build_scoring_framework(calculate_strategic=False)
            ml_scores = analyzer.scores_df.copy()
        else:
            ml_scores = enhanced_scores.copy()
            print("机器学习优化失败，使用增强方法结果")
    else:
        ml_scores = enhanced_scores.copy()
        print("无订单数据，跳过机器学习优化")
    
    # 对比分析
    print("\n4. 方法对比分析")
    print("-" * 30)
    
    # 比较评分分布
    compare_score_distributions(traditional_scores, enhanced_scores, ml_scores)
    
    # 比较特征重要性
    compare_feature_importance(traditional_vectors, enhanced_vectors)
    
    # 比较相似度计算效果
    compare_similarity_effectiveness(analyzer, traditional_vectors, enhanced_vectors)
    
    # 生成对比报告
    generate_comparison_report(traditional_scores, enhanced_scores, ml_scores, 
                             traditional_time, enhanced_time)

def compare_score_distributions(traditional_scores, enhanced_scores, ml_scores):
    """比较不同方法的评分分布"""
    plt.figure(figsize=(15, 5))
    
    # 传统方法评分分布
    plt.subplot(1, 3, 1)
    plt.hist(traditional_scores['poi_score'], bins=20, alpha=0.7, color='blue', edgecolor='black')
    plt.title('传统方法评分分布')
    plt.xlabel('POI评分')
    plt.ylabel('场站数量')
    plt.grid(axis='y', alpha=0.3)
    
    # 增强方法评分分布
    plt.subplot(1, 3, 2)
    plt.hist(enhanced_scores['poi_score'], bins=20, alpha=0.7, color='green', edgecolor='black')
    plt.title('增强方法评分分布')
    plt.xlabel('POI评分')
    plt.ylabel('场站数量')
    plt.grid(axis='y', alpha=0.3)
    
    # 机器学习优化评分分布
    plt.subplot(1, 3, 3)
    plt.hist(ml_scores['poi_score'], bins=20, alpha=0.7, color='red', edgecolor='black')
    plt.title('机器学习优化评分分布')
    plt.xlabel('POI评分')
    plt.ylabel('场站数量')
    plt.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('output/poi_methods_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("评分分布对比图已保存到 output/poi_methods_comparison.png")

def compare_feature_importance(traditional_vectors, enhanced_vectors):
    """比较特征重要性"""
    print(f"传统方法特征数量: {traditional_vectors.shape[1]}")
    print(f"增强方法特征数量: {enhanced_vectors.shape[1]}")
    
    # 计算特征方差（作为重要性指标）
    traditional_variance = traditional_vectors.var().sort_values(ascending=False)
    enhanced_variance = enhanced_vectors.var().sort_values(ascending=False)
    
    # 可视化特征方差对比
    plt.figure(figsize=(15, 6))
    
    plt.subplot(1, 2, 1)
    top_traditional = traditional_variance.head(20)
    plt.barh(range(len(top_traditional)), top_traditional.values, color='blue', alpha=0.7)
    plt.yticks(range(len(top_traditional)), top_traditional.index, fontsize=8)
    plt.xlabel('特征方差')
    plt.title('传统方法 - Top 20 特征方差')
    plt.gca().invert_yaxis()
    
    plt.subplot(1, 2, 2)
    top_enhanced = enhanced_variance.head(20)
    plt.barh(range(len(top_enhanced)), top_enhanced.values, color='green', alpha=0.7)
    plt.yticks(range(len(top_enhanced)), top_enhanced.index, fontsize=8)
    plt.xlabel('特征方差')
    plt.title('增强方法 - Top 20 特征方差')
    plt.gca().invert_yaxis()
    
    plt.tight_layout()
    plt.savefig('output/feature_variance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("特征方差对比图已保存到 output/feature_variance_comparison.png")

def compare_similarity_effectiveness(analyzer, traditional_vectors, enhanced_vectors):
    """比较相似度计算的有效性"""
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.preprocessing import StandardScaler
    
    # 选择一个示例站点
    sample_station = traditional_vectors.index[0]
    print(f"使用 {sample_station} 作为示例站点进行相似度对比")
    
    # 传统方法相似度计算
    scaler = StandardScaler()
    traditional_scaled = scaler.fit_transform(traditional_vectors)
    traditional_sim = cosine_similarity(traditional_scaled)
    traditional_sim_df = pd.DataFrame(traditional_sim, 
                                    index=traditional_vectors.index, 
                                    columns=traditional_vectors.index)
    
    # 增强方法相似度计算
    enhanced_scaled = scaler.fit_transform(enhanced_vectors)
    enhanced_sim = cosine_similarity(enhanced_scaled)
    enhanced_sim_df = pd.DataFrame(enhanced_sim, 
                                 index=enhanced_vectors.index, 
                                 columns=enhanced_vectors.index)
    
    # 获取与示例站点最相似的站点
    traditional_similar = traditional_sim_df[sample_station].sort_values(ascending=False)[1:6]
    enhanced_similar = enhanced_sim_df[sample_station].sort_values(ascending=False)[1:6]
    
    print(f"\n传统方法 - 与 {sample_station} 最相似的5个站点:")
    for station, sim in traditional_similar.items():
        print(f"  {station}: {sim:.4f}")
    
    print(f"\n增强方法 - 与 {sample_station} 最相似的5个站点:")
    for station, sim in enhanced_similar.items():
        print(f"  {station}: {sim:.4f}")

def generate_comparison_report(traditional_scores, enhanced_scores, ml_scores, 
                             traditional_time, enhanced_time):
    """生成对比报告"""
    report = []
    report.append("# POI计算方法对比报告\n")
    report.append("## 1. 性能对比\n")
    
    # 计算统计指标
    traditional_stats = traditional_scores['poi_score'].describe()
    enhanced_stats = enhanced_scores['poi_score'].describe()
    ml_stats = ml_scores['poi_score'].describe()
    
    report.append("### 评分统计\n")
    report.append("| 方法 | 平均分 | 标准差 | 最小值 | 最大值 | 计算时间(秒) |")
    report.append("|------|--------|--------|--------|--------|-------------|")
    report.append(f"| 传统方法 | {traditional_stats['mean']:.2f} | {traditional_stats['std']:.2f} | {traditional_stats['min']:.2f} | {traditional_stats['max']:.2f} | {traditional_time:.2f} |")
    report.append(f"| 增强方法 | {enhanced_stats['mean']:.2f} | {enhanced_stats['std']:.2f} | {enhanced_stats['min']:.2f} | {enhanced_stats['max']:.2f} | {enhanced_time:.2f} |")
    report.append(f"| 机器学习优化 | {ml_stats['mean']:.2f} | {ml_stats['std']:.2f} | {ml_stats['min']:.2f} | {ml_stats['max']:.2f} | - |\n")
    
    report.append("## 2. 改进效果\n")
    
    # 计算改进幅度
    score_improvement = (enhanced_stats['std'] - traditional_stats['std']) / traditional_stats['std'] * 100
    report.append(f"- 评分区分度提升: {score_improvement:.1f}%\n")
    
    # 计算评分相关性
    correlation = traditional_scores['poi_score'].corr(enhanced_scores['poi_score'])
    report.append(f"- 传统方法与增强方法评分相关性: {correlation:.3f}\n")
    
    report.append("## 3. 主要改进点\n")
    report.append("1. **距离权重**: 考虑POI到充电站的距离，距离越近影响越大\n")
    report.append("2. **POI质量评估**: 根据POI名称、类型详细程度等判断重要性\n")
    report.append("3. **密度特征**: 增加POI密度、多样性等聚合特征\n")
    report.append("4. **机器学习优化**: 基于实际业绩数据自动优化权重\n")
    report.append("5. **避免重复计算**: 优化层次结构计算逻辑\n")
    
    # 保存报告
    with open('output/poi_enhancement_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("对比报告已保存到 output/poi_enhancement_report.md")

if __name__ == "__main__":
    compare_poi_calculation_methods()
