#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取POI数据
"""

import requests
import json
import os
import time
import pandas as pd

def get_poi_data(longitude, latitude, api_key, radius=3000, types=None):
    """
    获取POI数据

    参数:
        longitude: 经度
        latitude: 纬度
        api_key: 高德地图API密钥
        radius: 搜索半径（米）
        types: POI类型编码，多个类型用"|"分隔
    """
    # 构建API请求URL
    url = f"https://restapi.amap.com/v3/place/around?key={api_key}&location={longitude},{latitude}&radius={radius}&extensions=all&offset=50&page=1"

    # 如果指定了POI类型，则添加到URL中
    if types:
        url += f"&types={types}"

    print(f"请求URL: {url}")

    try:
        # 发送请求并获取响应
        response = requests.get(url)
        data = response.json()

        # 打印响应状态
        print(f"响应状态: {data.get('status')}")
        print(f"响应信息: {data.get('info', '无')}")
        print(f"响应数据数量: {data.get('count', '0')}")

        # 如果请求成功，处理POI数据
        if data.get('status') == '1':
            pois = data.get('pois', [])
            total_pois = int(data.get('count', '0'))
            print(f"获取到 {len(pois)} 个POI")

            # 如果有多页数据，继续获取
            if total_pois > 50:  # 每页最多50条数据
                total_pages = (total_pois + 49) // 50  # 向上取整

                for page in range(2, min(total_pages + 1, 6)):  # 最多获取前5页数据
                    page_url = f"https://restapi.amap.com/v3/place/around?key={api_key}&location={longitude},{latitude}&radius={radius}&extensions=all&offset=50&page={page}"

                    if types:
                        page_url += f"&types={types}"

                    try:
                        page_response = requests.get(page_url)
                        page_data = page_response.json()

                        if page_data.get('status') == '1':
                            page_pois = page_data.get('pois', [])
                            pois.extend(page_pois)
                            print(f"  获取第 {page} 页数据: {len(page_pois)} 个POI")
                        else:
                            print(f"  获取第 {page} 页数据失败: {page_data.get('info', '未知错误')}")
                    except Exception as e:
                        print(f"  获取第 {page} 页数据时出错: {e}")

                    # 添加延时，避免API调用过于频繁
                    time.sleep(0.5)

            # 打印前5个POI的信息
            for i, poi in enumerate(pois[:5]):
                print(f"\nPOI {i+1}:")
                print(f"  名称: {poi.get('name', '无')}")
                print(f"  类型: {poi.get('type', '无')}")
                print(f"  类型编码: {poi.get('typecode', '无')}")
                print(f"  地址: {poi.get('address', '无')}")
                print(f"  距离: {poi.get('distance', '无')}米")

            # 保存POI数据到文件
            os.makedirs('poi_data', exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"poi_data/poi_data_{longitude}_{latitude}_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({'data': {'pois': pois}}, f, ensure_ascii=False, indent=2)

            print(f"\nPOI数据已保存到 {filename}")

            return pois
        else:
            print(f"请求失败: {data.get('info', '未知错误')}")
            return []
    except Exception as e:
        print(f"请求出错: {e}")
        return []

def get_station_coordinates():
    """获取场站坐标"""
    print("\n正在获取场站坐标...")

    # 初始化结果字典
    stations = {}

    # 从final_stations_107.csv文件中获取场站坐标
    try:
        # 加载场站数据文件
        station_data_file = 'resource/final_stations_107.csv'
        if os.path.exists(station_data_file):
            station_data = pd.read_csv(station_data_file)

            # 检查列名
            if '名称' in station_data.columns:
                station_name_col = '名称'
            elif 'station_name' in station_data.columns:
                station_name_col = 'station_name'
            else:
                # 尝试找到包含"名称"或"name"的列
                station_cols = [col for col in station_data.columns if '名称' in col or 'name' in col.lower()]
                station_name_col = station_cols[0] if station_cols else None

            if station_name_col:
                # 使用高德坐标
                if '高德_经度' in station_data.columns and '高德_纬度' in station_data.columns:
                    lon_col, lat_col = '高德_经度', '高德_纬度'
                elif 'gcj02_lon' in station_data.columns and 'gcj02_lat' in station_data.columns:
                    lon_col, lat_col = 'gcj02_lon', 'gcj02_lat'
                else:
                    # 如果没有高德坐标，尝试使用普通经纬度
                    if '经度' in station_data.columns and '纬度' in station_data.columns:
                        lon_col, lat_col = '经度', '纬度'
                    else:
                        # 尝试找到包含"经度"或"lon"的列
                        lon_cols = [col for col in station_data.columns if '经度' in col or 'lon' in col.lower()]
                        lat_cols = [col for col in station_data.columns if '纬度' in col or 'lat' in col.lower()]
                        lon_col = lon_cols[0] if lon_cols else None
                        lat_col = lat_cols[0] if lat_cols else None

                if lon_col and lat_col:
                    for _, row in station_data.iterrows():
                        station_name = row[station_name_col]
                        if not pd.isna(row[lon_col]) and not pd.isna(row[lat_col]):
                            try:
                                longitude = float(row[lon_col])
                                latitude = float(row[lat_col])
                                stations[station_name] = {
                                    'longitude': longitude,
                                    'latitude': latitude
                                }
                            except (ValueError, TypeError):
                                pass  # 跳过无法转换为浮点数的值

                    print(f"  从final_stations_107.csv中获取了 {len(stations)} 个场站的坐标")
                else:
                    print(f"  final_stations_107.csv中没有经纬度列")
            else:
                print(f"  final_stations_107.csv中没有场站名称列")
        else:
            print(f"  场站数据文件 {station_data_file} 不存在")
    except Exception as e:
        print(f"  从final_stations_107.csv中提取坐标时出错: {e}")

    print(f"\n共获取到 {len(stations)} 个场站的坐标")
    return stations

def get_poi_types():
    """获取POI类型"""
    print("\n正在获取POI类型...")

    # 高相关性的大类
    high_relevance = [
        '01', # 汽车服务
        '15', # 交通设施
        '18'  # 道路附属设施
    ]

    # 中等相关性的大类
    medium_relevance = [
        '05', # 餐饮服务
        '06', # 购物
        '07', # 生活服务
        '12', # 商务住宅
        '14'  # 科教文化服务
    ]

    # 低相关性的大类
    low_relevance = [
        '08', # 体育休闲
        '09', # 医疗保健
        '10', # 住宿服务
        '11'  # 风景名胜
    ]

    # 不需要的大类
    excluded_categories = [
        '02', # 汽车销售
        '03', # 汽车维修
        '04', # 摩托车服务
        '19', # 事件活动
        '20', # 通行设施
        '22', # 地名地址信息
        '97', # 其他类别
        '98', # 其他类别
        '99'  # 其他类别
    ]

    # 加载高德POI分类编码
    try:
        poi_categories = pd.read_excel('resource/高德POI分类与编码（中英文）_V1.06_20230208.xlsx')
        poi_categories['NEW_TYPE'] = poi_categories['NEW_TYPE'].astype(str)
        print(f"已加载POI分类编码，共 {len(poi_categories)} 条记录")

        # 按相关性评分对POI类型进行分组
        poi_groups = {
            3: [],  # 高相关性
            2: [],  # 中等相关性
            1: [],  # 低相关性
            0: []   # 无相关性
        }

        # 为每个POI类型分配相关性评分
        for _, row in poi_categories.iterrows():
            typecode = row['NEW_TYPE']
            if len(typecode) == 6:
                large_category = typecode[:2]

                if large_category in excluded_categories:
                    # 排除不需要的大类
                    poi_groups[0].append(typecode)
                elif large_category in high_relevance:
                    poi_groups[3].append(typecode)
                elif large_category in medium_relevance:
                    poi_groups[2].append(typecode)
                elif large_category in low_relevance:
                    poi_groups[1].append(typecode)
                else:
                    poi_groups[0].append(typecode)

        # 打印相关性评分统计
        print(f"高相关性(3分): {len(poi_groups[3])} 个类型")
        print(f"中等相关性(2分): {len(poi_groups[2])} 个类型")
        print(f"低相关性(1分): {len(poi_groups[1])} 个类型")
        print(f"无相关性(0分): {len(poi_groups[0])} 个类型")

        return poi_groups
    except Exception as e:
        print(f"加载POI分类编码时出错: {e}")
        return {3: [], 2: [], 1: [], 0: []}

def main():
    """主函数"""
    print("POI数据获取工具")
    print("=" * 80)

    # 使用提供的API密钥
    api_key = "9bc017d3ba3c32eb6fb2d9fe0d224d96"

    # 获取场站坐标
    stations = get_station_coordinates()

    if not stations:
        print("没有场站坐标数据，无法获取POI数据")
        return

    # 获取POI类型
    poi_groups = get_poi_types()

    # 询问用户是否要限制场站数量
    limit_stations = input("\n是否要限制场站数量？(y/n): ")
    if limit_stations.lower() == 'y':
        try:
            num_stations = int(input("\n请输入要获取的场站数量: "))
            if num_stations < 1:
                num_stations = 1
            elif num_stations > len(stations):
                num_stations = len(stations)

            # 只使用前 num_stations 个场站
            station_items = list(stations.items())[:num_stations]
            stations = {name: coords for name, coords in station_items}
            print(f"\n将只获取 {len(stations)} 个场站的POI数据")
        except ValueError:
            print("\n输入无效，将获取所有场站的POI数据")

    # 询问用户要获取哪些相关性级别的POI类型
    print("\n请选择要获取的POI类型相关性级别:")
    print("1. 只获取高相关性POI类型")
    print("2. 获取高相关性和中等相关性POI类型")
    print("3. 获取高相关性、中等相关性和低相关性POI类型")

    try:
        level_choice = int(input("\n请选择 [1-3]: "))
        if level_choice < 1 or level_choice > 3:
            level_choice = 1
    except ValueError:
        level_choice = 1

    # 根据用户选择确定要获取的POI类型
    poi_types = []
    if level_choice >= 1:
        poi_types.extend(poi_groups[3])
    if level_choice >= 2:
        poi_types.extend(poi_groups[2])
    if level_choice >= 3:
        poi_types.extend(poi_groups[1])

    # 将POI类型转换为竖线分隔的字符串
    types_str = "|".join(poi_types)

    print(f"\n将获取 {len(poi_types)} 个POI类型的数据")

    # 创建日志目录
    log_dir = 'logs/poi_data'
    os.makedirs(log_dir, exist_ok=True)

    # 创建日志文件
    log_timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = f"{log_dir}/poi_fetch_{log_timestamp}.log"

    # 开始获取POI数据
    with open(log_file, 'w', encoding='utf-8') as log:
        log.write(f"POI数据获取日志 - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        log.write(f"API密钥: {api_key}\n")
        log.write(f"场站数量: {len(stations)}\n")
        log.write(f"POI类型数量: {len(poi_types)}\n")
        log.write("=" * 80 + "\n")

        total_fetched_pois = 0
        total_api_calls = 0

        for station_index, (station_name, station_info) in enumerate(stations.items()):
            station_start_time = time.time()
            longitude = station_info['longitude']
            latitude = station_info['latitude']

            print(f"\n正在获取场站 {station_index + 1}/{len(stations)}: {station_name} ({longitude}, {latitude}) 的POI数据...")
            log.write(f"\n场站: {station_name} ({longitude}, {latitude})\n")

            # 获取POI数据
            pois = get_poi_data(longitude, latitude, api_key, radius=3000, types=types_str)

            # 记录获取结果
            total_fetched_pois += len(pois)
            total_api_calls += 1  # 简化计算，实际上可能有多次API调用

            # 计算并记录场站处理时间
            station_time = time.time() - station_start_time
            log.write(f"获取到 {len(pois)} 个POI，处理时间: {station_time:.2f} 秒\n")

            # 添加延时，避免API调用过于频繁
            time.sleep(1)

        # 记录总结信息
        log.write(f"\n总结\n")
        log.write(f"总场站数: {len(stations)}\n")
        log.write(f"总POI类型数: {len(poi_types)}\n")
        log.write(f"总获取POI数: {total_fetched_pois}\n")
        log.write(f"总API调用次数: {total_api_calls}\n")
        log.write(f"完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

    print(f"\nPOI数据获取完成，共获取 {total_fetched_pois} 个POI，API调用 {total_api_calls} 次")
    print(f"日志已保存到 {log_file}")

if __name__ == "__main__":
    main()
