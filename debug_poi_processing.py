#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试POI处理过程
"""

import json
from collections import defaultdict
from integrated_station_analyzer import IntegratedStationAnalyzer

def debug_poi_processing():
    """调试POI处理过程"""
    
    # 1. 加载上海外滩的POI数据
    poi_file = 'raw_poi_data/新坐标_121.473701_31.230416_20250616_143501.json'
    with open(poi_file, 'r', encoding='utf-8') as f:
        poi_data = json.load(f)
    
    print("=== 上海外滩POI数据调试 ===")
    print(f"总POI数量: {len(poi_data['data']['pois'])}")
    
    # 2. 初始化分析器
    analyzer = IntegratedStationAnalyzer()
    analyzer.load_data()
    
    print(f"系统中有效POI编码数量: {len(analyzer.used_poi_codes)}")
    print("有效POI编码列表:")
    for code in sorted(analyzer.used_poi_codes):
        print(f"  {code}")
    
    # 3. 分析POI类型编码
    print("\n=== POI类型编码分析 ===")
    poi_types = {}
    for poi in poi_data['data']['pois']:
        if 'typecode' in poi:
            typecode = poi['typecode']
            name = poi.get('name', '未知')
            poi_type = poi.get('type', '未知')
            
            if typecode not in poi_types:
                poi_types[typecode] = []
            poi_types[typecode].append({'name': name, 'type': poi_type})
    
    print(f"发现的POI类型编码: {len(poi_types)}")
    for typecode, pois in poi_types.items():
        print(f"\n类型编码: {typecode}")
        print(f"  数量: {len(pois)}")
        print(f"  示例: {pois[0]['name']} ({pois[0]['type']})")
        
        # 检查是否在有效编码中
        large_category = typecode[:2] + '0000' if len(typecode) == 6 else None
        medium_category = typecode[:4] + '00' if len(typecode) == 6 else None
        
        in_system = False
        if typecode in analyzer.used_poi_codes:
            print(f"  ✓ 具体编码在系统中")
            in_system = True
        elif medium_category and medium_category in analyzer.used_poi_codes:
            print(f"  ✓ 中类编码 {medium_category} 在系统中")
            in_system = True
        elif large_category and large_category in analyzer.used_poi_codes:
            print(f"  ✓ 大类编码 {large_category} 在系统中")
            in_system = True
        else:
            print(f"  ✗ 不在系统中 (大类:{large_category}, 中类:{medium_category})")
    
    # 4. 模拟POI向量构建过程
    print("\n=== 模拟POI向量构建 ===")
    new_poi_vector = defaultdict(int)
    
    for poi in poi_data['data']['pois']:
        if 'typecode' in poi:
            typecode = poi['typecode']
            
            # 处理复合类型编码
            if '|' in typecode:
                typecodes = typecode.split('|')
            else:
                typecodes = [typecode]
            
            for tc in typecodes:
                if len(tc) == 6:
                    large_category = tc[:2] + '0000'
                    medium_category = tc[:4] + '00'
                    
                    # 优先使用最具体的编码
                    if tc in analyzer.used_poi_codes:
                        new_poi_vector[tc] += 1
                        print(f"  添加具体编码: {tc}")
                    elif medium_category in analyzer.used_poi_codes:
                        new_poi_vector[medium_category] += 1
                        print(f"  添加中类编码: {medium_category} (来自 {tc})")
                    elif large_category in analyzer.used_poi_codes:
                        new_poi_vector[large_category] += 1
                        print(f"  添加大类编码: {large_category} (来自 {tc})")
    
    print(f"\n构建的POI向量:")
    for code, count in new_poi_vector.items():
        print(f"  {code}: {count}")
    
    # 5. 计算评分
    print("\n=== 计算评分 ===")
    if analyzer.weights is None:
        analyzer.weights = analyzer._build_weights_from_used_poi()
    
    total_score = 0
    for code, count in new_poi_vector.items():
        weight = analyzer.weights.get(code, 0.5)
        score = count * weight
        total_score += score
        print(f"  {code}: {count} × {weight} = {score}")
    
    print(f"\n原始总分: {total_score}")
    
    # 6. 检查归一化（需要先构建POI向量）
    try:
        analyzer.build_poi_vectors()
        analyzer._calculate_all_station_raw_scores()

        normalized_score = (total_score / analyzer.max_raw_score) * 100 if analyzer.max_raw_score > 0 else 0
        print(f"最大原始分数: {analyzer.max_raw_score}")
        print(f"归一化后分数: {normalized_score}")
    except Exception as e:
        print(f"归一化计算失败: {e}")
        print("跳过归一化步骤")

if __name__ == "__main__":
    debug_poi_processing()
