#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POI计算配置文件
包含各种POI计算参数和权重配置
"""

import json
import os

class POIConfig:
    """POI计算配置类"""
    
    def __init__(self):
        """初始化配置"""
        # 距离权重配置
        self.distance_config = {
            'use_distance_weight': True,
            'decay_factor': 1000.0,  # 距离衰减因子（米）
            'max_distance': 3000,    # 最大有效距离（米）
            'min_weight': 0.1        # 最小权重
        }
        
        # POI质量评估配置
        self.quality_config = {
            'use_quality_weight': True,
            'name_length_factor': 0.2,      # 名称长度影响因子
            'type_detail_factor': 0.1,      # 类型详细程度影响因子
            'address_importance_factor': 0.1, # 地址重要性影响因子
            'min_quality_weight': 0.5,      # 最小质量权重
            'max_quality_weight': 2.0       # 最大质量权重
        }
        
        # 密度特征配置
        self.density_config = {
            'use_density_features': True,
            'density_radii': [500, 1000, 2000],  # 密度计算半径（米）
            'diversity_categories': ['05', '06', '08', '12', '15'],  # 多样性计算类别
            'commercial_types': ['05', '06', '08'],  # 商业类型
            'transport_types': ['15'],               # 交通类型
            'residential_types': ['12']              # 居住类型
        }
        
        # 机器学习优化配置
        self.ml_config = {
            'use_ml_optimization': True,
            'min_samples': 10,           # 最小样本数
            'cv_folds': 5,               # 交叉验证折数
            'random_state': 42,          # 随机种子
            'n_estimators': 100,         # 随机森林树数量
            'weight_range': (0.1, 3.0),  # 权重范围
            'feature_selection_threshold': 0.001  # 特征选择阈值
        }
        
        # 基础权重配置
        self.base_weights = {
            # 交通设施相关 - 高权重，对充电站很重要
            '150000': 1.0,   # 交通设施服务
            '150100': 2.5,   # 公交车站 - 提高权重
            '150200': 2.0,   # 地铁站 - 提高权重
            '150300': 1.5,   # 长途汽车站
            '150400': 1.2,   # 火车站
            '150500': 1.0,   # 机场
            '150700': 1.2,   # 高速路出口 - 提高权重
            '150800': 1.0,   # 服务区
            '150900': 1.0,   # 停车场
            
            # 商业设施相关 - 中高权重，带来客流
            '050000': 1.5,   # 餐饮服务 - 提高权重
            '050100': 1.6,   # 中餐厅 - 提高权重
            '050200': 1.4,   # 外国餐厅
            '050300': 1.3,   # 快餐店
            '050500': 1.2,   # 咖啡厅
            '060000': 1.2,   # 购物
            '060100': 1.5,   # 商场 - 提高权重
            '060200': 1.3,   # 超市/便利店 - 提高权重
            '060400': 1.0,   # 家居建材
            
            # 生活服务相关 - 中等权重
            '070000': 0.9,   # 生活服务
            '070200': 1.1,   # 汽车服务 - 提高权重
            '070500': 0.8,   # 邮局
            
            # 体育休闲相关 - 中等权重
            '080000': 0.8,   # 体育休闲
            '080100': 0.9,   # 体育场馆
            '080300': 0.8,   # 休闲娱乐
            '080500': 0.7,   # 度假村
            
            # 医疗保健相关 - 中等权重
            '100000': 0.7,   # 医疗保健
            '100100': 0.8,   # 医院
            '100200': 0.7,   # 诊所
            '100300': 0.6,   # 药店
            
            # 居住区相关 - 高权重，是主要客户来源
            '120000': 1.0,   # 商务住宅
            '120100': 1.1,   # 产业园区
            '120200': 1.8,   # 住宅区 - 大幅提高权重
            '120201': 1.9,   # 住宅区 - 大幅提高权重
            '120300': 1.4,   # 学校 - 提高权重
            '120400': 1.0,   # 楼宇
            
            # 汽车相关 - 最高权重，直接相关
            '010000': 2.0,   # 汽车服务 - 提高权重
            '010100': 2.5,   # 加油站 - 提高权重
            '011100': 3.0,   # 充电站 - 最高权重
        }
        
        # 时间权重配置（未来扩展）
        self.time_config = {
            'use_time_weight': False,
            'peak_hours': [7, 8, 9, 17, 18, 19],  # 高峰时段
            'peak_weight_multiplier': 1.2,         # 高峰时段权重倍数
            'weekend_weight_multiplier': 0.9       # 周末权重倍数
        }
    
    def save_config(self, filepath='output/poi_config.json'):
        """保存配置到文件"""
        config_dict = {
            'distance_config': self.distance_config,
            'quality_config': self.quality_config,
            'density_config': self.density_config,
            'ml_config': self.ml_config,
            'base_weights': self.base_weights,
            'time_config': self.time_config
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=4)
        
        print(f"POI配置已保存到 {filepath}")
    
    def load_config(self, filepath='output/poi_config.json'):
        """从文件加载配置"""
        if not os.path.exists(filepath):
            print(f"配置文件 {filepath} 不存在，使用默认配置")
            return
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            self.distance_config.update(config_dict.get('distance_config', {}))
            self.quality_config.update(config_dict.get('quality_config', {}))
            self.density_config.update(config_dict.get('density_config', {}))
            self.ml_config.update(config_dict.get('ml_config', {}))
            self.base_weights.update(config_dict.get('base_weights', {}))
            self.time_config.update(config_dict.get('time_config', {}))
            
            print(f"已从 {filepath} 加载POI配置")
        except Exception as e:
            print(f"加载配置文件时出错: {e}")
            print("使用默认配置")
    
    def get_distance_weight(self, distance):
        """计算距离权重"""
        if not self.distance_config['use_distance_weight']:
            return 1.0
        
        if distance > self.distance_config['max_distance']:
            return self.distance_config['min_weight']
        
        # 使用指数衰减函数
        import numpy as np
        weight = np.exp(-distance / self.distance_config['decay_factor'])
        return max(self.distance_config['min_weight'], weight)
    
    def get_quality_weight(self, poi):
        """计算POI质量权重"""
        if not self.quality_config['use_quality_weight']:
            return 1.0
        
        weight = 1.0
        
        # 名称长度影响
        name = poi.get('name', '')
        if len(name) > 10:
            weight += self.quality_config['name_length_factor']
        elif len(name) < 5:
            weight -= self.quality_config['name_length_factor']
        
        # 类型详细程度影响
        poi_type = poi.get('type', '')
        type_levels = poi_type.count(';')
        if type_levels >= 2:
            weight += self.quality_config['type_detail_factor']
        
        # 地址重要性影响
        address = poi.get('address', '')
        if any(keyword in address for keyword in ['路', '街', '大道', '广场']):
            weight += self.quality_config['address_importance_factor']
        
        # 限制权重范围
        return max(self.quality_config['min_quality_weight'], 
                  min(self.quality_config['max_quality_weight'], weight))
    
    def update_weights_from_ml(self, ml_weights):
        """使用机器学习结果更新权重"""
        if not self.ml_config['use_ml_optimization']:
            return
        
        # 合并机器学习权重和基础权重
        for feature, weight in ml_weights.items():
            if feature in self.base_weights:
                # 使用加权平均
                self.base_weights[feature] = 0.7 * self.base_weights[feature] + 0.3 * weight
            else:
                self.base_weights[feature] = weight
        
        print(f"已使用机器学习结果更新 {len(ml_weights)} 个权重")
    
    def get_feature_categories(self):
        """获取特征分类信息"""
        return {
            'transport': ['150000', '150100', '150200', '150300', '150400', '150500', '150700', '150800', '150900'],
            'commercial': ['050000', '050100', '050200', '050300', '050500', '060000', '060100', '060200', '060400'],
            'service': ['070000', '070200', '070500'],
            'recreation': ['080000', '080100', '080300', '080500'],
            'medical': ['100000', '100100', '100200', '100300'],
            'residential': ['120000', '120100', '120200', '120201', '120300', '120400'],
            'automotive': ['010000', '010100', '011100']
        }

# 创建全局配置实例
poi_config = POIConfig()

if __name__ == "__main__":
    # 保存默认配置
    poi_config.save_config()
    print("默认POI配置已创建")
