# POI数据获取与类型选择优化工具

这个工具用于优化充电站评估系统中的POI数据获取与类型选择。它可以帮助您分析当前POI数据的覆盖情况，获取更全面的POI数据，并基于数据驱动的方法选择最相关的POI类型。

## 功能特点

1. **分析当前POI数据的覆盖情况**
   - 统计当前数据中出现的POI类型
   - 分析大类覆盖情况
   - 识别缺失的POI类型

2. **确定与充电站相关的POI类型范围**
   - 基于专业知识为POI类型分配相关性评分
   - 高相关性：汽车服务、交通设施、道路附属设施
   - 中等相关性：餐饮服务、购物、生活服务、商务住宅、科教文化服务
   - 低相关性：体育休闲、医疗保健、住宿服务、风景名胜

3. **设计分批次API调用策略**
   - 按相关性评分对POI类型进行分组
   - 优先获取高相关性的POI类型
   - 避免获取与充电站完全无关的POI类型

4. **实现增量式POI数据获取**
   - 使用高德地图API获取POI数据
   - 按批次获取，避免API调用过于频繁
   - 将新获取的POI数据与现有数据合并

5. **基于获取的数据优化POI类型选择**
   - 使用随机森林特征重要性分析POI类型的预测价值
   - 选择重要性高的POI类型
   - 可视化特征重要性

## 使用方法

1. **安装依赖**

```bash
pip install pandas numpy scikit-learn matplotlib requests
```

2. **准备数据**

确保以下文件存在：
- `resource/高德POI分类与编码（中英文）_V1.06_20230208.xlsx`：高德POI分类编码表
- `resource/2025_Q2_10cols_107stations.csv`：第二季度充电站订单数据
- `resource/output_2025_Q1_10cols_107stations.csv`：第一季度充电站订单数据
- `resource/weightPOI`：当前使用的POI类型列表（可选）

3. **运行工具**

```bash
python poi_optimization.py
```

4. **查看结果**

工具会生成以下输出文件：
- `output/selected_poi_types.csv`：选择的POI类型及其重要性
- `output/weightPOI_optimized`：优化后的POI类型列表（可直接用于系统）
- `output/poi_feature_importance.png`：POI类型特征重要性可视化

## 注意事项

1. 高德地图API有调用次数限制，请合理使用
2. 获取POI数据可能需要较长时间，请耐心等待
3. 优化结果依赖于现有的订单数据，如果订单数据不足，可能影响优化效果

## 示例输出

```
POI数据获取与类型选择优化工具
================================================================================
正在加载数据...
已加载POI分类编码，共 915 条记录
已加载 175 个场站的原始POI数据
已加载 107 个场站的营业额数据

正在分析当前POI数据的覆盖情况...

当前POI数据覆盖情况：
总POI类型数: 915
当前数据中的POI类型数: 120
大类总数: 23
已覆盖大类数: 15
缺失大类数: 8
当前使用的POI类型数: 25

缺失的大类：
020000: 汽车销售
030000: 汽车维修
040000: 摩托车服务
130000: 政府机构
160000: 金融保险
170000: 公司企业
190000: 事件活动
200000: 通行设施
```

## 如何使用优化后的POI类型

1. 将 `output/weightPOI_optimized` 文件复制到 `resource` 目录下，并重命名为 `weightPOI`
2. 重新运行充电站评估系统，系统将使用优化后的POI类型

## 优化结果

使用两个季度的数据优化POI类型选择，得到了以下结果：

- **最重要的POI类型**：
  1. **150700** (公交车站相关) - 重要性: 0.007953
  2. **150900** (停车场相关) - 重要性: 0.006512
  3. **061207|141400** (未找到具体名称) - 重要性: 0.005170
  4. **141204** (幼儿园) - 重要性: 0.004852
  5. **150906** (路边停车场) - 重要性: 0.004334

- **交通设施类POI占据主导地位**：
  - 公交车站、停车场、火车站、地铁站等交通设施类POI在两个季度模型中占据了更重要的位置
  - 这表明充电站与交通设施的关联性更强，这符合直觉，因为充电站通常需要靠近交通枢纽

- **教育设施也很重要**：
  - 幼儿园、培训机构等教育设施在两个季度模型中也占据了重要位置
  - 这可能表明家长在接送孩子时会使用充电服务

## 季度间的变化分析

分析了场站在两个季度之间的营业额变化：

- **增长最快的场站**：
  1. 重庆二郎街道充电站：增长率3904.22%
  2. 易佳电普天科技站场站：增长率3647.72%
  3. 易佳电广汽能源超充站：增长率2219.77%

- **下降最快的场站**：
  1. 国网无锡电动汽车服务有限公司：下降99.97%
  2. 桐城人民政府：下降99.26%
  3. 安车集团移动充电车：下降97.49%

这些极端变化表明，某些场站可能受到了季节性因素、促销活动或其他外部因素的显著影响。

## 建议

1. **使用优化后的POI类型列表**：
   - 将 `output/weightPOI_optimized` 文件复制到 `resource` 目录下，并重命名为 `weightPOI`
   - 这将使系统使用更准确的POI类型列表进行评估

2. **关注交通设施和教育设施**：
   - 在选择新的充电站位置时，应优先考虑靠近公交车站、停车场、地铁站等交通设施
   - 靠近幼儿园、培训机构等教育设施的位置也值得考虑

3. **注意季节性变化**：
   - 某些场站的营业额在不同季节有显著变化，这可能需要在运营策略上做出相应调整
   - 可以考虑在不同季节采用不同的促销策略

## 进一步优化

如果您希望进一步优化POI类型选择，可以考虑：

1. 收集更多季度的充电站订单数据
2. 尝试不同的特征选择方法，如LASSO回归
3. 考虑POI的距离因素，为距离充电站更近的POI赋予更高的权重
4. 结合专业知识，手动调整自动选择的POI类型列表
