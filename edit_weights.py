#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POI权重编辑工具

此工具用于编辑POI权重文件，可以查看、修改和保存权重。
"""

import os
import json
import pandas as pd

def load_weights():
    """加载权重文件"""
    weights_file = 'output/poi_weights.json'
    if not os.path.exists(weights_file):
        print(f"权重文件 {weights_file} 不存在，请先运行 station_analysis.py 生成权重文件")
        return None
    
    try:
        with open(weights_file, 'r', encoding='utf-8') as f:
            weights = json.load(f)
        print(f"已加载 {len(weights)} 个POI权重")
        return weights
    except Exception as e:
        print(f"读取权重文件时出错: {e}")
        return None

def save_weights(weights):
    """保存权重文件"""
    weights_file = 'output/poi_weights.json'
    try:
        with open(weights_file, 'w', encoding='utf-8') as f:
            json.dump(weights, f, ensure_ascii=False, indent=4)
        print(f"已保存 {len(weights)} 个POI权重到 {weights_file}")
        return True
    except Exception as e:
        print(f"保存权重文件时出错: {e}")
        return False

def load_poi_categories():
    """加载POI分类编码"""
    try:
        poi_categories = pd.read_excel('resource/高德POI分类与编码（中英文）_V1.06_20230208.xlsx')
        print(f"已加载 {len(poi_categories)} 个POI分类编码")
        return poi_categories
    except Exception as e:
        print(f"读取POI分类编码文件时出错: {e}")
        return None

def get_category_name(code, poi_categories):
    """获取POI分类名称"""
    if poi_categories is None:
        return "未知分类"
    
    # 尝试查找完全匹配的编码
    matches = poi_categories[poi_categories['NEW_TYPE'] == code]
    if not matches.empty:
        return matches.iloc[0]['TYPE']
    
    # 如果是中类编码（末尾两位为00）
    if code.endswith('00') and len(code) == 6:
        # 查找所有以该中类开头的小类
        matches = poi_categories[poi_categories['NEW_TYPE'].str.startswith(code[:4])]
        if not matches.empty:
            # 取第一个匹配项的前两部分
            parts = matches.iloc[0]['TYPE'].split(';')
            if len(parts) >= 2:
                return ';'.join(parts[:2])
            return matches.iloc[0]['TYPE']
    
    # 如果是大类编码（中间和末尾四位为0）
    if code.endswith('0000') and len(code) == 6:
        # 查找所有以该大类开头的小类
        matches = poi_categories[poi_categories['NEW_TYPE'].str.startswith(code[:2])]
        if not matches.empty:
            # 取第一个匹配项的第一部分
            parts = matches.iloc[0]['TYPE'].split(';')
            if len(parts) >= 1:
                return parts[0]
            return matches.iloc[0]['TYPE']
    
    return "未知分类"

def display_weights(weights, poi_categories=None, filter_str=None):
    """显示权重"""
    if weights is None:
        return
    
    # 按编码排序
    sorted_codes = sorted(weights.keys())
    
    # 如果有过滤条件，则只显示匹配的编码
    if filter_str:
        filter_str = filter_str.lower()
        filtered_codes = []
        for code in sorted_codes:
            name = get_category_name(code, poi_categories).lower()
            if filter_str in code.lower() or filter_str in name:
                filtered_codes.append(code)
        sorted_codes = filtered_codes
    
    # 显示权重
    print("\n当前POI权重:")
    print("-" * 80)
    print(f"{'编码':<10} {'权重':<10} {'分类名称':<60}")
    print("-" * 80)
    
    for code in sorted_codes:
        name = get_category_name(code, poi_categories)
        print(f"{code:<10} {weights[code]:<10.2f} {name:<60}")
    
    print("-" * 80)
    print(f"共 {len(sorted_codes)} 个POI权重")

def edit_weight(weights, poi_categories=None):
    """编辑权重"""
    if weights is None:
        return
    
    while True:
        code = input("\n请输入要编辑的POI编码（输入'q'退出）: ").strip()
        if code.lower() == 'q':
            break
        
        if code not in weights:
            print(f"编码 {code} 不存在于权重字典中")
            add = input("是否添加该编码？(y/n): ").strip().lower()
            if add != 'y':
                continue
        else:
            name = get_category_name(code, poi_categories)
            print(f"当前编码: {code}, 分类: {name}, 权重: {weights.get(code, 0.0)}")
        
        try:
            weight = float(input("请输入新的权重值: ").strip())
            weights[code] = weight
            print(f"已更新编码 {code} 的权重为 {weight}")
        except ValueError:
            print("无效的权重值，请输入一个数字")
    
    save = input("\n是否保存修改？(y/n): ").strip().lower()
    if save == 'y':
        save_weights(weights)

def batch_edit_weights(weights, poi_categories=None):
    """批量编辑权重"""
    if weights is None:
        return
    
    print("\n批量编辑权重")
    print("可以使用以下格式批量编辑权重:")
    print("1. 编辑大类: 输入大类编码（如'150000'）和权重，将更新该大类及其所有未设置权重的中类和小类")
    print("2. 编辑中类: 输入中类编码（如'150100'）和权重，将更新该中类及其所有未设置权重的小类")
    print("3. 编辑小类: 输入小类编码（如'150101'）和权重，将只更新该小类")
    
    code = input("\n请输入要编辑的POI编码: ").strip()
    if not code:
        return
    
    try:
        weight = float(input("请输入新的权重值: ").strip())
    except ValueError:
        print("无效的权重值，请输入一个数字")
        return
    
    # 更新权重
    updated = 0
    
    # 如果是大类编码（末尾四位为0）
    if code.endswith('0000') and len(code) == 6:
        # 更新大类权重
        weights[code] = weight
        updated += 1
        
        # 更新所有以该大类开头的中类和小类（如果它们没有自己的权重）
        prefix = code[:2]
        for existing_code in list(weights.keys()):
            if existing_code.startswith(prefix) and existing_code != code:
                weights[existing_code] = weight
                updated += 1
        
        # 如果有POI分类编码数据，还可以添加该大类下的所有中类和小类
        if poi_categories is not None:
            for _, row in poi_categories.iterrows():
                if 'NEW_TYPE' in row and isinstance(row['NEW_TYPE'], str) and row['NEW_TYPE'].startswith(prefix):
                    if row['NEW_TYPE'] not in weights:
                        weights[row['NEW_TYPE']] = weight
                        updated += 1
    
    # 如果是中类编码（末尾两位为00）
    elif code.endswith('00') and len(code) == 6:
        # 更新中类权重
        weights[code] = weight
        updated += 1
        
        # 更新所有以该中类开头的小类（如果它们没有自己的权重）
        prefix = code[:4]
        for existing_code in list(weights.keys()):
            if existing_code.startswith(prefix) and existing_code != code:
                weights[existing_code] = weight
                updated += 1
        
        # 如果有POI分类编码数据，还可以添加该中类下的所有小类
        if poi_categories is not None:
            for _, row in poi_categories.iterrows():
                if 'NEW_TYPE' in row and isinstance(row['NEW_TYPE'], str) and row['NEW_TYPE'].startswith(prefix):
                    if row['NEW_TYPE'] not in weights:
                        weights[row['NEW_TYPE']] = weight
                        updated += 1
    
    # 如果是小类编码
    else:
        # 只更新该小类
        weights[code] = weight
        updated += 1
    
    print(f"已更新 {updated} 个POI编码的权重")
    
    save = input("\n是否保存修改？(y/n): ").strip().lower()
    if save == 'y':
        save_weights(weights)

def main():
    """主函数"""
    print("POI权重编辑工具")
    print("=" * 50)
    
    # 加载权重
    weights = load_weights()
    if weights is None:
        return
    
    # 加载POI分类编码
    poi_categories = load_poi_categories()
    
    while True:
        print("\n请选择操作:")
        print("1. 显示所有权重")
        print("2. 搜索权重")
        print("3. 编辑单个权重")
        print("4. 批量编辑权重")
        print("5. 保存权重")
        print("0. 退出")
        
        choice = input("\n请输入选项: ").strip()
        
        if choice == '1':
            display_weights(weights, poi_categories)
        elif choice == '2':
            filter_str = input("请输入搜索关键词: ").strip()
            display_weights(weights, poi_categories, filter_str)
        elif choice == '3':
            edit_weight(weights, poi_categories)
        elif choice == '4':
            batch_edit_weights(weights, poi_categories)
        elif choice == '5':
            save_weights(weights)
        elif choice == '0':
            break
        else:
            print("无效的选项，请重新输入")
    
    print("\n感谢使用POI权重编辑工具！")

if __name__ == "__main__":
    main()
