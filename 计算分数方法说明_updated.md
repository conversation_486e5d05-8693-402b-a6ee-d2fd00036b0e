# 场站评分计算方法详细说明

## 1. 概述

本文档详细说明了新建场站决策辅助系统中的评分计算方法。该方法基于POI（兴趣点）数据，通过分析场站周围的各类设施分布情况，为场站位置提供综合评分，帮助决策者评估新建场站的适合度。

## 2. 数据准备

### 2.1 POI数据收集

系统使用高德地图API获取的POI数据，每个场站周围3公里范围内的POI信息被收集并存储在JSON文件中。每个POI包含以下关键信息：

- `typecode`：POI类别编码，如"150100"表示公交车站
- `type`：POI类别描述，如"交通设施服务;公交车站;公交车站"
- `name`：POI名称
- `distance`：POI到场站的距离（米）
- `location`：POI的经纬度坐标

### 2.2 POI向量构建

为了进行定量分析，系统将每个场站的POI数据转换为POI向量：

1. 统计每个POI类别的数量
2. 创建一个向量，其中每个维度对应一个POI类别
3. 向量中的值表示该类别POI的数量

例如，一个场站的POI向量可能如下：
```
{
  "150100": 15,  # 公交车站15个
  "150200": 2,   # 地铁站2个
  "050000": 30,  # 餐饮服务30个
  ...
}
```

## 3. 评分计算流程

### 3.1 权重字典构建

系统基于"使用POI"文件中的内容构建权重字典，确保大类能够兼容其下所有的中类和小类。权重构建流程如下：

1. **定义基础权重**：为常用的POI类别定义基础权重值
2. **提取分类层次关系**：从高德POI分类编码中提取大类、中类和小类的层次关系
3. **应用大类权重**：如果大类在使用的POI编码中，则将其权重应用到所有中类和小类（除非它们有自己的权重）
4. **应用中类权重**：如果中类在使用的POI编码中，则将其权重应用到所有小类（除非它们有自己的权重）
5. **处理小类权重**：如果小类没有自己的权重，则尝试使用其所属中类或大类的权重

这种方法确保了权重的一致性和完整性，即使某些中类或小类没有明确定义权重，也能从其所属大类继承权重。

### 3.2 POI类别权重设置

不同类别的POI根据其对充电站运营的重要性被赋予不同的权重。权重考虑了大类、中类和小类的分级：

#### 交通设施相关

| POI类别 | 类别代码 | 类别级别 | 权重 | 说明 |
|---------|----------|---------|------|------|
| 交通设施服务 | 150000 | 大类 | 1.0 | 交通设施的基础权重 |
| 公交车站 | 150100 | 中类 | 2.0 | 公共交通枢纽，客流量大 |
| 地铁站 | 150200 | 中类 | 1.5 | 固定客流，使用频率高 |
| 长途汽车站 | 150300 | 中类 | 1.2 | 中距离旅客，使用频率中等 |
| 火车站 | 150400 | 中类 | 1.0 | 长途旅客，使用频率较低 |
| 机场 | 150500 | 中类 | 0.9 | 长途旅客，使用频率较低 |
| 高速路出口 | 150700 | 中类 | 1.0 | 便于长途电动车充电 |
| 服务区 | 150800 | 中类 | 0.9 | 高速公路服务区，适合长途充电 |
| 停车场 | 150900 | 中类 | 0.8 | 提供停车便利 |

#### 商业设施相关

| POI类别 | 类别代码 | 类别级别 | 权重 | 说明 |
|---------|----------|---------|------|------|
| 餐饮服务 | 050000 | 大类 | 1.2 | 用户充电等待时可用餐 |
| 中餐厅 | 050100 | 中类 | 1.3 | 用餐时间较长，适合充电等待 |
| 外国餐厅 | 050200 | 中类 | 1.2 | 用餐时间较长，适合充电等待 |
| 快餐店 | 050300 | 中类 | 1.1 | 用餐时间较短，适合快速充电 |
| 咖啡厅 | 050500 | 中类 | 1.0 | 适合充电等待时休息 |
| 购物 | 060000 | 大类 | 1.0 | 用户充电等待时可购物 |
| 商场 | 060100 | 中类 | 1.2 | 购物时间较长，适合充电等待 |
| 超市/便利店 | 060200 | 中类 | 1.1 | 提供日常生活必需品 |
| 家居建材 | 060400 | 中类 | 0.9 | 购物时间较长，适合充电等待 |
| 生活服务 | 070000 | 大类 | 0.8 | 提供日常服务需求 |
| 汽车服务 | 070200 | 中类 | 0.9 | 与汽车相关的服务，吸引车主 |
| 邮局 | 070500 | 中类 | 0.8 | 提供邮政服务 |
| 体育休闲 | 080000 | 大类 | 0.7 | 提供休闲娱乐选择 |
| 体育场馆 | 080100 | 中类 | 0.8 | 运动时间较长，适合充电等待 |
| 休闲娱乐 | 080300 | 中类 | 0.7 | 娱乐时间较长，适合充电等待 |
| 度假村 | 080500 | 中类 | 0.6 | 旅游目的地，适合长时间停留 |
| 医疗保健 | 100000 | 大类 | 0.6 | 提供基础医疗保障 |
| 医院 | 100100 | 中类 | 0.7 | 就医时间较长，适合充电等待 |
| 诊所 | 100200 | 中类 | 0.6 | 就诊时间较短 |
| 药店 | 100300 | 中类 | 0.5 | 购药时间较短 |

#### 居住区相关

| POI类别 | 类别代码 | 类别级别 | 权重 | 说明 |
|---------|----------|---------|------|------|
| 商务住宅 | 120000 | 大类 | 0.8 | 商务住宅区域 |
| 产业园区 | 120100 | 中类 | 0.9 | 办公人员充电需求 |
| 住宅区 | 120200 | 中类 | 1.4 | 固定居民，夜间充电需求 |
| 住宅区 | 120201 | 小类 | 1.5 | 固定居民，夜间充电需求 |
| 学校 | 120300 | 中类 | 1.2 | 教职工充电需求 |
| 楼宇 | 120400 | 中类 | 0.9 | 办公人员充电需求 |

#### 其他重要类别

| POI类别 | 类别代码 | 类别级别 | 权重 | 说明 |
|---------|----------|---------|------|------|
| 汽车服务 | 010000 | 大类 | 1.5 | 与汽车相关的服务 |
| 加油站 | 010100 | 中类 | 1.8 | 与充电站功能互补 |
| 充电站 | 011100 | 中类 | 2.0 | 相同功能，可能存在竞争关系 |

注：上表仅列出了部分重要的POI类别权重，实际系统中包含更多类别的权重设置。

### 3.3 评分计算公式

场站的评分计算采用加权求和的方法，具体公式如下：

```
Score = Σ(POI_count_i * Weight_i) / Max_possible_score * 100
```

其中：
- `POI_count_i`：第i类POI的数量
- `Weight_i`：第i类POI的权重
- `Max_possible_score`：理论最大可能得分，用于归一化

### 3.4 计算步骤详解

以下是评分计算的详细步骤：

1. **初始化得分**：
   ```python
   score = 0
   ```

2. **遍历POI向量**：
   ```python
   for typecode, count in poi_vector.items():
       # 获取大类编码（前两位）
       large_category_code = typecode[:2] + '0000'
       
       # 获取中类编码（前四位）
       medium_category_code = typecode[:4] + '00'
       
       # 优先级：具体编码 > 中类编码 > 大类编码 > 默认权重
       weight = self.weights.get(typecode, 
                 self.weights.get(medium_category_code, 
                   self.weights.get(large_category_code, 0.5)))
       
       # 累加得分
       score += count * weight
   ```

3. **归一化得分**：
   ```python
   # 计算理论最大可能得分
   max_possible_score = sum([poi_vector.max() * max(self.weights.values()) for _ in range(len(self.weights))])
   
   # 归一化到0-100范围
   normalized_score = (score / max_possible_score) * 100 if max_possible_score > 0 else 0
   ```

4. **返回最终得分**：
   ```python
   return normalized_score
   ```

### 3.5 代码实现

以下是评分计算方法的完整代码实现：

```python
def evaluate_station_score(self, station_name):
    """评估场站的综合得分"""
    if station_name not in self.poi_vectors.index:
        print(f"场站 '{station_name}' 不在数据集中")
        return None
    
    # 获取场站的POI向量
    poi_vector = self.poi_vectors.loc[station_name]
    
    # 如果权重字典未初始化，则构建它
    if self.weights is None:
        self.weights = self._build_weights_from_used_poi()
    
    # 计算加权得分
    score = 0
    for typecode, count in poi_vector.items():
        # 获取大类编码（前两位）
        large_category_code = typecode[:2] + '0000'
        
        # 获取中类编码（前四位）
        medium_category_code = typecode[:4] + '00'
        
        # 优先级：具体编码 > 中类编码 > 大类编码 > 默认权重
        weight = self.weights.get(typecode, 
                  self.weights.get(medium_category_code, 
                    self.weights.get(large_category_code, 0.5)))
        
        # 累加得分
        score += count * weight
    
    # 归一化得分（0-100）
    max_possible_score = sum([poi_vector.max() * max(self.weights.values()) for _ in range(len(self.weights))])
    normalized_score = (score / max_possible_score) * 100 if max_possible_score > 0 else 0
    
    return normalized_score
```

## 4. 权重字典构建详解

权重字典构建是评分系统的核心部分，它确保了大类能够兼容其下所有的中类和小类。以下是构建过程的详细说明：

### 4.1 基础权重定义

首先，我们定义了一组基础权重，包括常用的大类、中类和小类的权重值：

```python
base_weights = {
    # 交通设施相关
    '150000': 1.0,  # 交通设施服务
    '150100': 2.0,  # 公交车站
    # ... 其他权重
}
```

### 4.2 提取分类层次关系

从高德POI分类编码中提取大类、中类和小类的层次关系：

```python
category_hierarchy = {}

if hasattr(self, 'poi_categories') and self.poi_categories is not None:
    if 'NEW_TYPE' in self.poi_categories.columns:
        for _, row in self.poi_categories.iterrows():
            code = row['NEW_TYPE']
            if len(code) == 6:  # 确保是6位编码
                large_category = code[:2] + '0000'  # 大类
                medium_category = code[:4] + '00'   # 中类
                
                # 添加到分类层次关系中
                if large_category not in category_hierarchy:
                    category_hierarchy[large_category] = {'medium': set(), 'small': set()}
                
                category_hierarchy[large_category]['medium'].add(medium_category)
                category_hierarchy[large_category]['small'].add(code)
                
                if medium_category not in category_hierarchy:
                    category_hierarchy[medium_category] = {'small': set()}
                
                category_hierarchy[medium_category]['small'].add(code)
```

### 4.3 应用大类权重

如果大类在使用的POI编码中，则将其权重应用到所有中类和小类（除非它们有自己的权重）：

```python
if large_category in self.used_poi_codes and large_category in base_weights:
    large_weight = base_weights[large_category]
    
    # 如果大类在分类层次关系中
    if large_category in category_hierarchy:
        # 将大类权重应用到所有中类（除非中类有自己的权重）
        for medium in category_hierarchy[large_category]['medium']:
            if medium not in base_weights:
                weights[medium] = large_weight
        
        # 将大类权重应用到所有小类（除非小类有自己的权重）
        for small in category_hierarchy[large_category]['small']:
            if small not in base_weights:
                weights[small] = large_weight
```

### 4.4 应用中类权重

如果中类在使用的POI编码中，则将其权重应用到所有小类（除非它们有自己的权重）：

```python
if medium_category in self.used_poi_codes and medium_category in base_weights:
    medium_weight = base_weights[medium_category]
    
    # 如果中类在分类层次关系中
    if medium_category in category_hierarchy:
        # 将中类权重应用到所有小类（除非小类有自己的权重）
        for small in category_hierarchy[medium_category]['small']:
            if small not in base_weights:
                weights[small] = medium_weight
```

### 4.5 处理小类权重

如果小类没有自己的权重，则尝试使用其所属中类或大类的权重：

```python
if code in self.used_poi_codes and code not in weights:
    if medium_category in weights:
        weights[code] = weights[medium_category]
    elif large_category in weights:
        weights[code] = weights[large_category]
    else:
        weights[code] = 0.5  # 默认权重
```

## 5. 评分解释

### 5.1 评分范围

系统的评分范围为0-100分，具体解释如下：

| 分数范围 | 评级 | 建议 |
|---------|------|------|
| 90-100 | 极佳 | 非常适合建设充电站 |
| 80-89 | 优秀 | 适合建设充电站 |
| 70-79 | 良好 | 建议建设充电站 |
| 60-69 | 一般 | 可考虑建设充电站 |
| 50-59 | 较差 | 谨慎考虑建设充电站 |
| 0-49 | 不适合 | 不建议建设充电站 |

### 5.2 评分因素分析

评分主要考虑以下几个方面：

1. **交通便利性**：
   - 公共交通设施（公交站、地铁站等）的数量和分布
   - 道路交通设施（高速出口、主干道等）的可达性
   - 停车设施的完善程度

2. **商业活跃度**：
   - 餐饮、购物等商业设施的密度
   - 生活服务、休闲娱乐设施的多样性
   - 医疗、教育等公共服务设施的覆盖

3. **人口密度**：
   - 住宅区的规模和分布
   - 办公楼宇的集中度
   - 学校、医院等人流密集场所的存在

### 5.3 评分示例

以下是几个评分示例：

1. **高分场站示例**：
   - 位于商业中心，周围有大型购物中心、写字楼
   - 交通便利，靠近地铁站、多条公交线路
   - 周边有大型住宅区
   - 评分：85分，建议建设

2. **中分场站示例**：
   - 位于城市次中心，有一定商业设施
   - 交通一般，有公交但无地铁
   - 周边有部分住宅区
   - 评分：65分，可考虑建设

3. **低分场站示例**：
   - 位于城市边缘，商业设施稀少
   - 交通不便，公共交通少
   - 周边人口稀少
   - 评分：40分，不建议建设

## 6. 新场站预测

### 6.1 相似度计算

对于新场站，系统通过计算其与现有场站的相似度来预测其适合度：

1. **标准化POI向量**：
   ```python
   scaler = StandardScaler()
   scaled_vectors = scaler.fit_transform(combined_vectors)
   ```

2. **计算余弦相似度**：
   ```python
   similarities = cosine_similarity([scaled_vectors[new_station_index]], scaled_vectors[:-1])[0]
   ```

3. **找出最相似的场站**：
   ```python
   similarity_series = pd.Series(similarities, index=self.poi_vectors.index)
   top_similar = similarity_series.sort_values(ascending=False).head(top_n)
   ```

### 6.2 预测得分计算

新场站的预测得分基于与其最相似的现有场站的得分：

1. **获取相似场站的得分**：
   ```python
   similar_station_scores = []
   for station in similar_stations.index:
       score = analyzer.evaluate_station_score(station)
       similar_station_scores.append((station, score))
   ```

2. **计算平均得分**：
   ```python
   avg_score = sum([score for _, score in similar_station_scores]) / len(similar_station_scores)
   ```

3. **给出建议**：
   ```python
   if avg_score >= 70:
       recommendation = "非常适合"
   elif avg_score >= 60:
       recommendation = "适合"
   elif avg_score >= 50:
       recommendation = "一般"
   else:
       recommendation = "不太适合"
   ```

## 7. 评分框架的优化方向

### 7.1 权重优化

当前权重设置基于经验判断，未来可通过以下方式优化：

1. **数据驱动的权重调整**：
   - 分析现有高绩效场站的POI分布
   - 使用机器学习方法自动调整权重

2. **区域差异化权重**：
   - 针对不同城市或区域设置不同权重
   - 考虑城市规模、发展水平等因素

### 7.2 评分模型扩展

未来评分模型可扩展以下维度：

1. **时间维度**：
   - 考虑不同时段的POI活跃度
   - 分析工作日与周末的差异

2. **距离衰减**：
   - 引入距离衰减函数，近距离POI权重更高
   - 根据POI类型设置不同的衰减系数

3. **竞争因素**：
   - 考虑周边已有充电站的影响
   - 分析市场饱和度

### 7.3 数据源扩展

未来可整合更多数据源：

1. **人口统计数据**：
   - 人口密度
   - 收入水平
   - 电动车保有量

2. **交通流量数据**：
   - 道路流量
   - 高峰期分布
   - 周边停车时长

3. **充电订单数据**：
   - 充电频率
   - 充电时长
   - 用户留存率

## 8. 结论

本评分计算方法提供了一个基于POI数据的场站评估框架，通过对场站周围环境的定量分析，为决策者提供客观参考。该方法具有以下特点：

1. **数据驱动**：基于实际POI数据，减少主观判断
2. **可解释性**：评分过程透明，结果可解释
3. **可扩展性**：框架设计灵活，可整合更多数据源
4. **实用性**：直接支持新场站选址决策
5. **层次兼容**：确保大类权重向下兼容所有中类和小类，保持权重体系的一致性

需要注意的是，评分结果仅作为决策参考，实际选址还需考虑土地成本、电网接入条件等其他因素。
