#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置文件

此模块用于存储和管理项目的配置信息，包括API密钥等
"""

import os
import json
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.absolute()

# 配置文件路径
CONFIG_FILE = PROJECT_ROOT / 'config.json'

# 默认配置结构（不包含敏感信息）
DEFAULT_CONFIG_STRUCTURE = {
    "api_keys": {
        "deepseek": None,  # DeepSeek API密钥
        "gaode": None  # 高德地图API密钥
    },
    "model_settings": {
        "deepseek_model": "deepseek-chat",
        "max_tokens": 1000,
        "temperature": 0.6
    },
    "paths": {
        "output_dir": "output",
        "resource_dir": "resource",
        "poi_data_dir": "raw_poi_data"
    }
}

def load_config():
    """
    加载配置文件，如果不存在或缺少必要信息则抛出提示

    返回:
        配置字典

    异常:
        FileNotFoundError: 如果配置文件不存在
        ValueError: 如果配置文件缺少必要信息
    """
    # 如果配置文件不存在，抛出异常
    if not CONFIG_FILE.exists():
        raise FileNotFoundError(f"配置文件 {CONFIG_FILE} 不存在，请创建配置文件并添加必要的API密钥")

    # 读取配置文件
    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 检查必要的配置项是否存在
        if "api_keys" not in config:
            raise ValueError("配置文件缺少 'api_keys' 部分")

        if "deepseek" not in config["api_keys"] or config["api_keys"]["deepseek"] is None:
            raise ValueError("配置文件缺少 DeepSeek API密钥，请在配置文件中添加")

        if "gaode" not in config["api_keys"] or config["api_keys"]["gaode"] is None:
            raise ValueError("配置文件缺少高德地图 API密钥，请在配置文件中添加")

        # 确保其他必要的配置项存在
        if "paths" not in config:
            config["paths"] = DEFAULT_CONFIG_STRUCTURE["paths"]
            print("配置文件缺少 'paths' 部分，使用默认路径配置")

        if "model_settings" not in config:
            config["model_settings"] = DEFAULT_CONFIG_STRUCTURE["model_settings"]
            print("配置文件缺少 'model_settings' 部分，使用默认模型设置")

        return config
    except json.JSONDecodeError:
        raise ValueError(f"配置文件 {CONFIG_FILE} 格式错误，不是有效的JSON")
    except Exception as e:
        raise Exception(f"读取配置文件时出错: {e}")

def save_config(config):
    """
    保存配置到文件

    参数:
        config: 配置字典
    """
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        print(f"配置已保存到 {CONFIG_FILE}")
    except Exception as e:
        print(f"保存配置文件时出错: {e}")

def get_api_key(service):
    """
    获取指定服务的API密钥

    参数:
        service: 服务名称，如 'deepseek', 'gaode'

    返回:
        API密钥字符串

    异常:
        ValueError: 如果指定服务的API密钥不存在
    """
    try:
        config = load_config()
        api_key = config.get('api_keys', {}).get(service)

        if api_key is None:
            raise ValueError(f"未找到 {service} 的API密钥，请在配置文件中添加")

        return api_key
    except Exception as e:
        # 重新抛出异常，保留原始错误信息
        raise

def set_api_key(service, api_key):
    """
    设置指定服务的API密钥

    参数:
        service: 服务名称，如 'deepseek', 'gaode'
        api_key: API密钥字符串
    """
    config = load_config()

    # 确保api_keys字典存在
    if 'api_keys' not in config:
        config['api_keys'] = {}

    # 设置API密钥
    config['api_keys'][service] = api_key

    # 保存配置
    save_config(config)
    print(f"已设置 {service} API密钥")

# 尝试导出常用配置项
try:
    # 尝试从配置文件加载API密钥
    DEEPSEEK_API_KEY = get_api_key('deepseek')
    GAODE_API_KEY = get_api_key('gaode')

    # 尝试从配置文件加载路径
    config = load_config()
    OUTPUT_DIR = config.get('paths', {}).get('output_dir', 'output')
    RESOURCE_DIR = config.get('paths', {}).get('resource_dir', 'resource')
    POI_DATA_DIR = config.get('paths', {}).get('poi_data_dir', 'raw_poi_data')

    # 确保必要的目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(POI_DATA_DIR, exist_ok=True)
except Exception as e:
    # 如果配置文件不存在或格式错误，使用默认路径
    print(f"警告: {e}")
    print("使用默认路径配置")
    OUTPUT_DIR = DEFAULT_CONFIG_STRUCTURE['paths']['output_dir']
    RESOURCE_DIR = DEFAULT_CONFIG_STRUCTURE['paths']['resource_dir']
    POI_DATA_DIR = DEFAULT_CONFIG_STRUCTURE['paths']['poi_data_dir']

    # 确保必要的目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(POI_DATA_DIR, exist_ok=True)

    # API密钥将在需要时抛出异常
    DEEPSEEK_API_KEY = None
    GAODE_API_KEY = None
