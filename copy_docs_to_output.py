#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
将说明文档复制到 output 文件夹中
"""

import os
import shutil

def main():
    """主函数"""
    # 确保 output 目录存在
    os.makedirs('output', exist_ok=True)
    
    # 要复制的文档列表
    docs = [
        'README.md',
        '计算分数方法说明_updated.md',
        '多维度评分框架说明.md',
    ]
    
    # 复制文档
    for doc in docs:
        if os.path.exists(doc):
            dest_path = os.path.join('output', doc.replace('_updated', ''))
            shutil.copy2(doc, dest_path)
            print(f"已将 {doc} 复制到 {dest_path}")
        else:
            print(f"文件 {doc} 不存在")
    
    print("\n所有文档已复制到 output 文件夹")

if __name__ == "__main__":
    main()
