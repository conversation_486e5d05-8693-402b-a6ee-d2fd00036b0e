#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电站新位置评估工具

此脚本用于评估给定坐标的充电站适合度，包括：
1. 从坐标获取周边POI数据
2. 计算POI评分
3. 预测业绩表现
4. 计算综合评分
5. 可视化评估结果
"""

import os
import sys
import json
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from station_analysis import StationAnalyzer
from evaluate_coordinates import fetch_poi_data_from_coordinates, evaluate_coordinates, visualize_evaluation_results

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='充电站新位置评估工具')
    parser.add_argument('--longitude', type=float, help='经度')
    parser.add_argument('--latitude', type=float, help='纬度')
    parser.add_argument('--radius', type=int, default=3000, help='搜索半径（米），默认1000米')
    parser.add_argument('--api_key', type=str, default='9bc017d3ba3c32eb6fb2d9fe0d224d96', help='高德地图API密钥')
    parser.add_argument('--order_data', type=str, default='resource/2025_Q2_10cols_107stations.csv', help='订单数据文件路径')
    parser.add_argument('--interactive', action='store_true', help='交互模式')

    return parser.parse_args()

def interactive_mode():
    """交互模式"""
    print("\n充电站新位置评估工具 - 交互模式")
    print("=" * 50)

    while True:
        try:
            # 获取用户输入
            longitude = input("\n请输入经度（例如：121.473701）或输入 'q' 退出: ")
            if longitude.lower() == 'q':
                break
            longitude = float(longitude)

            latitude = input("请输入纬度（例如：31.230416）: ")
            latitude = float(latitude)

            radius_input = input("请输入搜索半径（米），默认3000米: ")
            radius = int(radius_input) if radius_input.strip() else 1000

            # 初始化分析器
            analyzer = StationAnalyzer(order_data_file='resource/2025_Q2_10cols_107stations.csv')

            # 加载数据
            analyzer.load_data()

            # 构建POI向量
            analyzer.build_poi_vectors()

            # 计算相似度
            analyzer.calculate_similarity()

            # 构建评分框架
            analyzer.build_scoring_framework()

            # 评估坐标
            prediction_results = evaluate_coordinates(longitude, latitude, analyzer, radius=radius)

            if prediction_results:
                # 可视化评估结果
                visualize_evaluation_results(prediction_results, analyzer)

                # 打印评估结果
                print("\n评估结果:")
                print(f"坐标: ({longitude}, {latitude})")
                print(f"POI评分: {prediction_results['poi_score']:.2f}")

                # 添加战略价值评分的显示
                if 'strategic_score' in prediction_results:
                    print(f"战略价值评分: {prediction_results['strategic_score']:.2f}")

                if prediction_results['performance_score'] is not None:
                    print(f"业绩评分: {prediction_results['performance_score']:.2f}")
                    print(f"综合评分: {prediction_results['combined_score']:.2f}")

                if prediction_results['predicted_revenue'] is not None:
                    print(f"预测周营业额: {prediction_results['predicted_revenue']:.2f}元")

                print(f"建议: {prediction_results['recommendation']}建设充电站")

                print("\n最相似的场站:")
                for station, score in prediction_results['similar_stations']:
                    print(f"- {station}: 评分 {score:.2f}")
            else:
                print("评估失败")

            # 询问是否继续
            continue_input = input("\n是否评估另一个位置？(y/n): ")
            if continue_input.lower() != 'y':
                break

        except ValueError as e:
            print(f"输入错误: {e}")
        except Exception as e:
            print(f"发生错误: {e}")

def main():
    """主函数"""
    args = parse_arguments()

    # 交互模式
    if args.interactive:
        interactive_mode()
        return

    # 命令行模式
    if args.longitude is None or args.latitude is None:
        print("错误: 必须提供经度和纬度")
        print("用法: python evaluate_new_location.py --longitude 121.473701 --latitude 31.230416 [--radius 1000]")
        print("或者使用交互模式: python evaluate_new_location.py --interactive")
        return

    print("充电站新位置评估工具")
    print("=" * 50)

    # 初始化分析器
    analyzer = StationAnalyzer(order_data_file=args.order_data)

    # 加载数据
    analyzer.load_data()

    # 构建POI向量
    analyzer.build_poi_vectors()

    # 计算相似度
    analyzer.calculate_similarity()

    # 构建评分框架
    analyzer.build_scoring_framework()

    # 评估坐标
    prediction_results = evaluate_coordinates(args.longitude, args.latitude, analyzer, api_key=args.api_key, radius=args.radius)

    if prediction_results:
        # 可视化评估结果
        visualize_evaluation_results(prediction_results, analyzer)

        # 打印评估结果
        print("\n评估结果:")
        print(f"坐标: ({args.longitude}, {args.latitude})")
        print(f"POI评分: {prediction_results['poi_score']:.2f}")

        # 添加战略价值评分的显示
        if 'strategic_score' in prediction_results:
            print(f"战略价值评分: {prediction_results['strategic_score']:.2f}")

        if prediction_results['performance_score'] is not None:
            print(f"业绩评分: {prediction_results['performance_score']:.2f}")
            print(f"综合评分: {prediction_results['combined_score']:.2f}")

        if prediction_results['predicted_revenue'] is not None:
            print(f"预测周营业额: {prediction_results['predicted_revenue']:.2f}元")

        print(f"建议: {prediction_results['recommendation']}建设充电站")

        print("\n最相似的场站:")
        for station, score in prediction_results['similar_stations']:
            print(f"- {station}: 评分 {score:.2f}")
    else:
        print("评估失败")

if __name__ == "__main__":
    main()
