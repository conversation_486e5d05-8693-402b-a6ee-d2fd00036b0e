#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DeepSeek API调用模块

此模块用于调用DeepSeek API生成充电站评估报告内容
"""

import os
import json
import re
import requests
import datetime
from typing import Dict, Any, List, Optional, Tuple
import config
import prompt_templates
import format_config

class DeepSeekAPI:
    """DeepSeek API调用类"""

    # 各部分标准标题格式
    SECTION_TITLES = {
        "概览": "<h2>评估概览</h2>",
        "POI分析": "<h3>POI评分深度分析</h3>",
        "战略价值分析": "<h3>战略价值深度分析</h3>",
        "业绩分析": "<h3>业绩分析与预测</h3>",
        "综合评分分析": "<h3>综合评分深度分析</h3>",
        "相似场站分析": "<h2>相似场站分析</h2>",
        "建议与结论": "<h2>建议与结论</h2>"
    }

    def __init__(self, api_key: Optional[str] = None):
        """
        初始化DeepSeek API调用类

        参数:
            api_key: DeepSeek API密钥，如果为None，则从配置文件或环境变量获取
        """
        # 优先级：传入的API密钥 > 配置文件中的API密钥 > 环境变量中的API密钥
        self.api_key = api_key or config.DEEPSEEK_API_KEY or os.environ.get("DEEPSEEK_API_KEY")
        if not self.api_key:
            print("警告: 未提供DeepSeek API密钥，将使用模板默认内容")

        # 从配置文件获取API URL和模型设置
        self.api_url = "https://api.deepseek.com/v1/chat/completions"

        # 获取模型设置
        model_settings = config.load_config().get('model_settings', {})
        self.model = model_settings.get('deepseek_model', "deepseek-chat")
        self.default_max_tokens = model_settings.get('max_tokens', 1000)
        self.default_temperature = model_settings.get('temperature', 0.6)

    def generate_content(self, prompt: str, max_tokens: int = None) -> str:
        """
        调用DeepSeek API生成内容

        参数:
            prompt: 提示词
            max_tokens: 最大生成token数，如果为None则使用配置中的默认值

        返回:
            生成的内容
        """
        if not self.api_key:
            return "未提供DeepSeek API密钥，无法生成内容"

        # 使用传入的max_tokens或配置中的默认值
        max_tokens = max_tokens or self.default_max_tokens

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": self.default_temperature
        }

        # 创建日志目录
        log_dir = "logs/deepseek_api"
        os.makedirs(log_dir, exist_ok=True)

        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # 记录请求内容
        request_log_file = f"{log_dir}/deepseek_request_{timestamp}.json"
        with open(request_log_file, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"已记录请求内容到: {request_log_file}")

        try:
            response = requests.post(self.api_url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()

            # 记录响应内容
            response_log_file = f"{log_dir}/deepseek_response_{timestamp}.json"
            with open(response_log_file, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            print(f"已记录响应内容到: {response_log_file}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"调用DeepSeek API时出错: {e}")

            # 记录错误信息
            error_log_file = f"{log_dir}/deepseek_error_{timestamp}.txt"
            with open(error_log_file, "w", encoding="utf-8") as f:
                f.write(f"错误信息: {e}\n")
                f.write(f"请求内容: {json.dumps(data, ensure_ascii=False, indent=2)}")

            print(f"已记录错误信息到: {error_log_file}")

            return f"生成内容时出错: {e}"

    def generate_report_section(self, section_name: str, evaluation_data: Dict[str, Any], template: str) -> str:
        """
        生成报告的特定部分

        参数:
            section_name: 部分名称
            evaluation_data: 评估数据
            template: 模板内容

        返回:
            生成的内容
        """
        # 使用标准化的提示模板
        prompt = prompt_templates.get_prompt_for_section(section_name, evaluation_data, template)

        # 创建日志目录
        log_dir = "logs/reports"
        os.makedirs(log_dir, exist_ok=True)

        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # 安全的部分名称（用于文件名）
        safe_section_name = section_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace("*", "_").replace("?", "_").replace("\"", "_").replace("<", "_").replace(">", "_").replace("|", "_")

        # 记录部分名称和模板
        section_log_file = f"{log_dir}/section_{safe_section_name}_{timestamp}.json"
        with open(section_log_file, "w", encoding="utf-8") as f:
            json.dump({
                "section_name": section_name,
                "template": template,
                "prompt": prompt
            }, f, ensure_ascii=False, indent=2)

        print(f"已记录部分信息到: {section_log_file}")

        # 生成内容
        content = self.generate_content(prompt)

        # 对内容进行后处理，确保格式一致性
        content = self._post_process_content(content, section_name)

        return content

    def _post_process_content(self, content: str, section_name: str) -> str:
        """
        对LLM生成的内容进行后处理，确保格式一致性

        参数:
            content: LLM生成的原始内容
            section_name: 部分名称

        返回:
            处理后的内容
        """
        # 如果内容为空或错误信息，直接返回
        if not content or content.startswith("生成内容时出错"):
            return content

        # 1. 确保使用标准的标题格式
        if section_name in self.SECTION_TITLES:
            standard_title = self.SECTION_TITLES[section_name]
            # 检查内容是否已经包含标准标题
            if standard_title not in content:
                # 尝试替换可能的变体标题
                title_pattern = r'<h[23]>.*?(?:评估|分析|概览|建议|结论).*?</h[23]>'
                content = re.sub(title_pattern, standard_title, content, count=1, flags=re.IGNORECASE)

        # 2. 确保数值格式一致
        # 替换评分格式，确保保留2位小数
        content = re.sub(r'(\d+\.\d)(?!\d)', r'\g<1>0', content)  # 将X.X格式转为X.X0

        # 处理金额格式
        # 更精确的金额模式匹配，避免错误格式化
        # 分别处理不同的金额格式模式

        # 1. 处理错误的千位分隔符格式（如3.00,330.69）
        wrong_format_pattern = r'(\d+)\.(\d{2}),(\d{3})\.(\d{2})'
        def fix_wrong_format(match):
            # 将3.00,330.69转换为正确的3330.69
            part1 = match.group(1)  # 3
            part2 = match.group(2)  # 00
            part3 = match.group(3)  # 330
            part4 = match.group(4)  # 69

            # 重新组合为正确的数字
            correct_num = float(f"{part1}{part2}{part3}.{part4}")
            return f"{correct_num:,.2f}"

        content = re.sub(wrong_format_pattern, fix_wrong_format, content)

        # 2. 处理正常的金额格式
        # 匹配模式：数字(可能包含小数点和千位分隔符)后跟"元"，或"¥"符号后跟数字
        money_pattern = r'(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?|\d+(?:\.\d{1,2})?)(?=元)|(?<=¥)(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?|\d+(?:\.\d{1,2})?)'

        def format_money(match):
            # 获取匹配的数字部分
            money_str = match.group(1) if match.group(1) is not None else match.group(2)

            # 移除已有的千位分隔符，然后转换为浮点数
            try:
                num = float(money_str.replace(',', ''))
                # 格式化为带千位分隔符的金额，保留2位小数
                return f"{num:,.2f}"
            except ValueError:
                # 如果转换失败，返回原始字符串
                return money_str

        content = re.sub(money_pattern, format_money, content)

        # 3. 确保HTML结构一致
        # 确保每个部分都包含在div中
        if not content.strip().startswith('<div'):
            # 根据部分名称选择合适的容器类
            section_class = ""

            # 从format_config中获取可用的容器类名
            available_classes = format_config.HTML_TAGS["容器"]["div"]

            # 根据部分名称选择合适的容器类
            if "概览" in section_name and "overview-section" in available_classes:
                section_class = "overview-section"
            elif "POI" in section_name and "poi-section" in available_classes:
                section_class = "poi-section"
            elif "战略" in section_name and "strategic-analysis" in available_classes:
                section_class = "strategic-analysis"
            elif "业绩" in section_name and "performance-metrics" in available_classes:
                section_class = "performance-metrics"
            elif "综合" in section_name and "analysis-section" in available_classes:
                section_class = "analysis-section"
            elif "相似" in section_name and "similar-stations" in available_classes:
                section_class = "similar-stations"
            elif ("建议" in section_name or "结论" in section_name) and "recommendation-section" in available_classes:
                section_class = "recommendation-section"
            else:
                # 默认类名
                section_class = section_name.replace('分析', '').replace('概览', '').lower() + "-section"

            content = f'<div class="{section_class}">\n{content}\n</div>'

        # 4. 确保表格格式一致
        # 添加border属性到没有的表格
        table_format = format_config.HTML_TAGS["表格"]["格式"]
        content = re.sub(r'<table(?!\s+border)', f'<table {table_format}', content)

        # 5. 确保列表格式一致
        # 确保无序列表使用<ul>而不是<dl>
        content = content.replace('<dl>', '<ul>').replace('</dl>', '</ul>')

        # 6. 确保结构完整性
        # 检查是否包含该部分应有的所有结构
        section_structure = format_config.get_section_structure(section_name)
        if section_structure:
            # 这里只是检查，不做强制修改，因为强制修改可能会破坏内容
            missing_sections = []
            for structure_item in section_structure:
                if structure_item.lower() not in content.lower():
                    missing_sections.append(structure_item)

            if missing_sections:
                print(f"警告: {section_name}部分缺少以下结构: {', '.join(missing_sections)}")

        return content

    def generate_full_report(self, evaluation_data: Dict[str, Any], templates: Dict[str, str]) -> Dict[str, str]:
        """
        生成完整的报告内容

        参数:
            evaluation_data: 评估数据
            templates: 各部分的模板内容

        返回:
            各部分生成的内容
        """
        # 创建日志目录
        log_dir = "logs/reports"
        os.makedirs(log_dir, exist_ok=True)

        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # 记录完整的评估数据
        evaluation_log_file = f"{log_dir}/evaluation_data_{timestamp}.json"
        with open(evaluation_log_file, "w", encoding="utf-8") as f:
            json.dump(evaluation_data, f, ensure_ascii=False, indent=2)

        print(f"已记录评估数据到: {evaluation_log_file}")

        # 记录所有模板
        templates_log_file = f"{log_dir}/templates_{timestamp}.json"
        with open(templates_log_file, "w", encoding="utf-8") as f:
            json.dump(templates, f, ensure_ascii=False, indent=2)

        print(f"已记录模板数据到: {templates_log_file}")

        report_sections = {}

        for section_name, template in templates.items():
            print(f"正在生成 {section_name} 部分...")
            content = self.generate_report_section(section_name, evaluation_data, template)
            report_sections[section_name] = content

            # 记录生成的内容
            content_log_file = f"{log_dir}/content_{section_name.replace('/', '_')}_{timestamp}.html"
            with open(content_log_file, "w", encoding="utf-8") as f:
                f.write(content)

            print(f"已记录生成内容到: {content_log_file}")

        # 记录完整的报告内容
        report_log_file = f"{log_dir}/full_report_{timestamp}.json"
        with open(report_log_file, "w", encoding="utf-8") as f:
            json.dump(report_sections, f, ensure_ascii=False, indent=2)

        print(f"已记录完整报告内容到: {report_log_file}")

        return report_sections
