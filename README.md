# 新建场站决策辅助功能 - 阶段一

## 项目概述

本项目旨在开发一个新建充电场站决策辅助系统，帮助决策者评估新建场站的适合度。当前为阶段一开发，主要基于POI（兴趣点）数据构建多维度评分框架，通过计算场站之间的相似度，预测新场站位置的适合度。

## 功能特点

1. **POI数据分析**：分析现有场站周围的POI数据，构建POI向量
2. **多维度评分框架**：基于POI类别及其权重构建评分框架
3. **场站相似度计算**：计算场站之间的相似度，找出相似场站
4. **新场站预测**：预测新场站位置的适合度
5. **可视化分析**：可视化场站得分和相似度

## 数据文件说明

- `场站POI数据_valid.xlsx`：已有的场站基础信息与通过高德API获取的POI信息
- `高德POI分类与编码（中英文）_V1.06_20230208.xlsx`：高德官方POI分类与编码
- `使用POI.docx`：决定使用的POI编码（大类、中类和小类）
- `raw_poi_data/`：通过高德API获取的每个场站3千米内的POI数据（JSON格式）

## 使用方法

1. 确保已安装所需的Python库：
   ```
   pip install pandas numpy scikit-learn matplotlib
   ```

2. 运行主程序：
   ```
   python station_analysis.py
   ```

3. 查看生成的结果文件：
   - `poi_vectors.csv`：场站POI向量
   - `similarity_matrix.csv`：场站相似度矩阵
   - `station_scores.csv`：场站综合得分
   - `station_scores.png`：场站得分可视化
   - `similarity_[场站名].png`：特定场站的相似度可视化

## 多维度评分框架说明

本项目构建了一个基于POI数据的多维度评分框架，主要考虑以下几个维度：

1. **交通便利性**：
   - 公交车站、地铁站、火车站等交通设施的数量和距离
   - 高速路出口、停车场等设施的可达性

2. **商业活跃度**：
   - 餐饮服务、购物中心、生活服务等商业设施的密度
   - 体育休闲、医疗保健等配套设施的完善程度

3. **人口密度**：
   - 住宅区、学校、办公楼等建筑的分布情况

每个POI类别根据其对充电站运营的重要性被赋予不同的权重，最终计算出场站的综合得分。

## 后续开发计划

本项目为阶段一开发，后续将扩展以下功能：

1. **人口数据整合**：基于坐标获取附近人口数量信息
2. **充电订单分析**：分析现有场站的充电订单信息，包括：
   - 充电用户特征
   - 订单时长、电量、金额
   - 充电时间分布
   - 电池状态（SOC）变化

3. **机器学习模型优化**：
   - 引入更复杂的机器学习模型
   - 优化特征工程
   - 提高预测准确性

4. **交互式决策支持系统**：
   - 开发Web界面
   - 提供可视化决策支持工具

## 注意事项

- 当前版本仅使用POI数据进行分析，预测结果仅供参考
- 实际决策应综合考虑更多因素，如土地成本、电网接入条件等
- 评分框架中的权重设置可根据实际业务需求进行调整
