#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
订单数据分析模块

此模块用于分析充电站订单数据，提取业绩指标，并与POI数据结合，改进场站评分系统。
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Arial Unicode MS', 'Kaiti SC', 'STFangsong', 'STHeiti']  # 按优先级排序的中文字体列表
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Arial Unicode MS', 'Kaiti SC', 'STFangsong', 'STHeiti']  # 按优先级排序的中文字体列表
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

from datetime import datetime, timedelta
from collections import defaultdict
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

class OrderAnalyzer:
    """订单数据分析器"""

    def __init__(self, order_file='resource/2025_Q2_10cols_107stations.csv'):
        """初始化订单分析器"""
        self.order_file = order_file #csv中实际有117个场站数据,不包括高速服务区
        self.orders = None
        self.station_metrics = None
        self.station_hourly_patterns = None
        self.station_daily_patterns = None
        self.equipment_counts = None  # 每个站的设备数量
        self.performance_model = None  # 性能预测模型

        # 确保输出目录存在
        os.makedirs('output', exist_ok=True)

        # 加载订单数据
        self.load_orders()

    def load_orders(self):
        """加载订单数据"""
        print(f"正在加载订单数据: {self.order_file}")

        try:
            # 读取CSV文件
            self.orders = pd.read_csv(self.order_file)

            # 转换时间列为datetime类型
            self.orders['start_time'] = pd.to_datetime(self.orders['start_time'])
            self.orders['end_time'] = pd.to_datetime(self.orders['end_time'])

            # 添加日期和小时列
            self.orders['date'] = self.orders['start_time'].dt.date
            self.orders['hour'] = self.orders['start_time'].dt.hour
            self.orders['weekday'] = self.orders['start_time'].dt.weekday
            self.orders['is_weekend'] = self.orders['weekday'] >= 5

            # 计算充电时长（分钟）
            self.orders['charge_minutes'] = self.orders['timelong'] / 60

            print(f"已加载 {len(self.orders)} 条订单记录，涉及 {self.orders['station_name'].nunique()} 个充电站")

            # 估算每个站的设备数量（基于pile_no的唯一值数量）
            self.estimate_equipment_counts()

        except Exception as e:
            print(f"加载订单数据时出错: {e}")
            self.orders = None

    def estimate_equipment_counts(self):
        """估算每个站的设备数量"""
        if self.orders is None:
            return

        # 统计每个站的唯一充电桩数量
        equipment_counts = self.orders.groupby('station_name')['pile_no'].nunique()
        self.equipment_counts = equipment_counts.to_dict()

        print(f"已估算 {len(self.equipment_counts)} 个充电站的设备数量")

    def calculate_station_metrics(self):
        """计算每个充电站的业绩指标"""
        if self.orders is None:
            print("订单数据未加载，无法计算业绩指标")
            return None

        print("正在计算充电站业绩指标...")

        # 按充电站分组计算各种指标
        metrics = []

        for station_name, station_orders in self.orders.groupby('station_name'):
            # 基础指标
            total_orders = len(station_orders)
            total_revenue = station_orders['total_fees'].sum()
            total_charge_power = station_orders['charge_power'].sum()
            total_charge_minutes = station_orders['charge_minutes'].sum()

            # 设备数量和利用率
            equipment_count = self.equipment_counts.get(station_name, 1)
            # 一周总分钟数 = 设备数 * 单台设备满电可放电时长 * 7天
            total_available_minutes = equipment_count * 184 * 7 #额定电量184kWh，功率60kw，假设每日补电一次的情况下，单日可使用184分钟
            # 将充电分钟转换为小时计算设备利用率
            equipment_utilization = total_charge_minutes  / total_available_minutes

            # 每日指标
            daily_orders = station_orders.groupby('date').size()
            daily_revenue = station_orders.groupby('date')['total_fees'].sum()

            avg_daily_orders = daily_orders.mean()
            avg_daily_revenue = daily_revenue.mean()
            max_daily_revenue = daily_revenue.max()
            min_daily_revenue = daily_revenue.min()
            revenue_std = daily_revenue.std()

            # 工作日vs周末
            weekday_orders = station_orders[~station_orders['is_weekend']]
            weekend_orders = station_orders[station_orders['is_weekend']]

            weekday_revenue = weekday_orders['total_fees'].sum()
            weekend_revenue = weekend_orders['total_fees'].sum()

            # 计算每天的平均值（考虑到工作日有5天，周末有2天）
            avg_weekday_revenue = weekday_revenue / 5 if len(weekday_orders) > 0 else 0
            avg_weekend_revenue = weekend_revenue / 2 if len(weekend_orders) > 0 else 0
            weekend_ratio = avg_weekend_revenue / avg_weekday_revenue if avg_weekday_revenue > 0 else 0

            # 高峰期分析
            hourly_revenue = station_orders.groupby('hour')['total_fees'].sum()
            peak_hour = hourly_revenue.idxmax() if not hourly_revenue.empty else 0
            peak_hour_revenue = hourly_revenue.max() if not hourly_revenue.empty else 0

            # 平均订单指标
            avg_order_revenue = total_revenue / total_orders if total_orders > 0 else 0
            avg_order_power = total_charge_power / total_orders if total_orders > 0 else 0
            avg_order_duration = total_charge_minutes / total_orders if total_orders > 0 else 0  # 平均充电时长（分钟）

            # 收集所有指标
            metrics.append({
                'station_name': station_name,
                'total_orders': total_orders,
                'total_revenue': total_revenue,
                'total_charge_power': total_charge_power,
                'total_charge_minutes': total_charge_minutes,
                'equipment_count': equipment_count,
                'equipment_utilization': equipment_utilization,
                'avg_daily_orders': avg_daily_orders,
                'avg_daily_revenue': avg_daily_revenue,
                'max_daily_revenue': max_daily_revenue,
                'min_daily_revenue': min_daily_revenue,
                'revenue_std': revenue_std,
                'weekday_revenue': weekday_revenue,
                'weekend_revenue': weekend_revenue,
                'avg_weekday_revenue': avg_weekday_revenue,
                'avg_weekend_revenue': avg_weekend_revenue,
                'weekend_ratio': weekend_ratio,
                'peak_hour': peak_hour,
                'peak_hour_revenue': peak_hour_revenue,
                'avg_order_revenue': avg_order_revenue,
                'avg_order_power': avg_order_power,
                'avg_order_duration': avg_order_duration,
                'revenue_per_equipment': total_revenue / equipment_count,
            })

        # 创建DataFrame
        self.station_metrics = pd.DataFrame(metrics)
        self.station_metrics.set_index('station_name', inplace=True)

        print(f"已计算 {len(self.station_metrics)} 个充电站的业绩指标")

        # 保存到CSV文件
        self.station_metrics.to_csv('output/station_metrics.csv')
        print("充电站业绩指标已保存到 output/station_metrics.csv")

        return self.station_metrics

    def calculate_hourly_patterns(self):
        """计算每个充电站的小时分布模式"""
        if self.orders is None:
            print("订单数据未加载，无法计算小时分布模式")
            return None

        print("正在计算充电站小时分布模式...")

        # 创建一个字典，用于存储每个站的小时分布
        hourly_patterns = {}

        for station_name, station_orders in self.orders.groupby('station_name'):
            # 按小时分组计算收入
            hourly_revenue = station_orders.groupby('hour')['total_fees'].sum()

            # 确保所有24小时都有数据
            full_hours = pd.Series(0, index=range(24))
            hourly_revenue = hourly_revenue.add(full_hours, fill_value=0)

            # 标准化为比例
            total_revenue = hourly_revenue.sum()
            if total_revenue > 0:
                hourly_pattern = hourly_revenue / total_revenue
            else:
                hourly_pattern = hourly_revenue

            hourly_patterns[station_name] = hourly_pattern

        # 创建DataFrame
        self.station_hourly_patterns = pd.DataFrame(hourly_patterns).T

        print(f"已计算 {len(self.station_hourly_patterns)} 个充电站的小时分布模式")

        # 保存到CSV文件
        self.station_hourly_patterns.to_csv('output/station_hourly_patterns.csv')
        print("充电站小时分布模式已保存到 output/station_hourly_patterns.csv")

        return self.station_hourly_patterns

    def calculate_daily_patterns(self):
        """计算每个充电站的日分布模式"""
        if self.orders is None:
            print("订单数据未加载，无法计算日分布模式")
            return None

        print("正在计算充电站日分布模式...")

        # 创建一个字典，用于存储每个站的日分布
        daily_patterns = {}

        for station_name, station_orders in self.orders.groupby('station_name'):
            # 按星期几分组计算收入
            daily_revenue = station_orders.groupby('weekday')['total_fees'].sum()

            # 确保所有7天都有数据
            full_days = pd.Series(0, index=range(7))
            daily_revenue = daily_revenue.add(full_days, fill_value=0)

            # 标准化为比例
            total_revenue = daily_revenue.sum()
            if total_revenue > 0:
                daily_pattern = daily_revenue / total_revenue
            else:
                daily_pattern = daily_revenue

            daily_patterns[station_name] = daily_pattern

        # 创建DataFrame
        self.station_daily_patterns = pd.DataFrame(daily_patterns).T
        self.station_daily_patterns.columns = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

        print(f"已计算 {len(self.station_daily_patterns)} 个充电站的日分布模式")

        # 保存到CSV文件
        self.station_daily_patterns.to_csv('output/station_daily_patterns.csv')
        print("充电站日分布模式已保存到 output/station_daily_patterns.csv")

        return self.station_daily_patterns

    def visualize_station_performance(self, station_name):
        """可视化充电站业绩数据"""
        if self.orders is None or station_name not in self.orders['station_name'].unique():
            print(f"没有找到充电站 {station_name} 的订单数据")
            return

        print(f"正在可视化充电站 {station_name} 的业绩数据...")

        # 获取该站的订单数据
        station_orders = self.orders[self.orders['station_name'] == station_name]

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 日营业额趋势
        daily_revenue = station_orders.groupby('date')['total_fees'].sum()
        axes[0, 0].plot(daily_revenue.index, daily_revenue.values, marker='o')
        axes[0, 0].set_title(f"{station_name} 日营业额趋势")
        axes[0, 0].set_ylabel("营业额")
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, linestyle='--', alpha=0.7)

        # 2. 小时分布
        hourly_revenue = station_orders.groupby('hour')['total_fees'].sum()
        axes[0, 1].bar(hourly_revenue.index, hourly_revenue.values)
        axes[0, 1].set_title("各小时段营业额")
        axes[0, 1].set_xlabel("小时")
        axes[0, 1].set_ylabel("营业额")
        axes[0, 1].set_xticks(range(0, 24, 2))
        axes[0, 1].grid(True, linestyle='--', alpha=0.7)

        # 3. 工作日vs周末
        weekday_weekend = station_orders.groupby('is_weekend')['total_fees'].sum()
        weekday_revenue = weekday_weekend.get(False, 0)
        weekend_revenue = weekday_weekend.get(True, 0)

        # 计算日均值
        avg_weekday = weekday_revenue / 5
        avg_weekend = weekend_revenue / 2

        axes[1, 0].bar(['工作日(日均)', '周末(日均)'], [avg_weekday, avg_weekend])
        axes[1, 0].set_title("工作日vs周末日均营业额")
        axes[1, 0].set_ylabel("日均营业额")
        axes[1, 0].grid(True, linestyle='--', alpha=0.7)

        # 4. 星期几分布
        weekday_revenue = station_orders.groupby('weekday')['total_fees'].sum()
        weekday_labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        weekday_values = [weekday_revenue.get(i, 0) for i in range(7)]

        axes[1, 1].bar(weekday_labels, weekday_values)
        axes[1, 1].set_title("星期几营业额分布")
        axes[1, 1].set_ylabel("营业额")
        axes[1, 1].grid(True, linestyle='--', alpha=0.7)

        plt.tight_layout()
        plt.savefig(f'output/{station_name}_performance.png', dpi=300)
        plt.close()

        print(f"充电站 {station_name} 的业绩可视化已保存到 output/{station_name}_performance.png")

    def visualize_top_stations(self, metric='total_revenue', top_n=20):
        """可视化表现最好的充电站"""
        if self.station_metrics is None:
            self.calculate_station_metrics()

        if self.station_metrics is None:
            print("无法获取充电站业绩指标")
            return

        print(f"正在可视化表现最好的 {top_n} 个充电站（按 {metric} 排序）...")

        # 获取排名前N的站
        top_stations = self.station_metrics.sort_values(by=metric, ascending=False).head(top_n)

        # 创建柱状图
        plt.figure(figsize=(12, 8))
        bars = plt.barh(top_stations.index, top_stations[metric], color='skyblue')

        # 添加数值标签
        for bar in bars:
            width = bar.get_width()
            plt.text(width * 1.01, bar.get_y() + bar.get_height()/2,
                    f'{width:.2f}', va='center')

        plt.xlabel(metric)
        plt.title(f'Top {top_n} 充电站（按 {metric} 排序）')
        plt.gca().invert_yaxis()  # 使排名第一的在顶部
        plt.grid(axis='x', linestyle='--', alpha=0.7)
        plt.tight_layout()

        plt.savefig(f'output/top_stations_{metric}.png', dpi=300)
        plt.close()

        print(f"表现最好的充电站可视化已保存到 output/top_stations_{metric}.png")

    def visualize_hourly_patterns(self):
        """可视化充电站小时分布模式"""
        if self.station_hourly_patterns is None:
            self.calculate_hourly_patterns()

        if self.station_hourly_patterns is None:
            print("无法获取充电站小时分布模式")
            return

        print("正在可视化充电站小时分布模式...")

        # 计算平均小时分布
        avg_pattern = self.station_hourly_patterns.mean()

        # 创建折线图
        plt.figure(figsize=(12, 6))
        plt.plot(avg_pattern.index, avg_pattern.values, marker='o', linewidth=2, color='blue')

        plt.xlabel('小时')
        plt.ylabel('收入比例')
        plt.title('充电站平均小时收入分布')
        plt.xticks(range(0, 24, 2))
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()

        plt.savefig('output/hourly_pattern_avg.png', dpi=300)
        plt.close()

        # 创建热力图
        plt.figure(figsize=(15, 10))
        sns.heatmap(self.station_hourly_patterns.sample(min(30, len(self.station_hourly_patterns))),
                   cmap='YlGnBu', linewidths=0.5)

        plt.xlabel('小时')
        plt.title('充电站小时收入分布热力图（随机30个站）')
        plt.tight_layout()

        plt.savefig('output/hourly_pattern_heatmap.png', dpi=300)
        plt.close()

        print("充电站小时分布模式可视化已保存")

    def visualize_daily_patterns(self):
        """可视化充电站日分布模式"""
        if self.station_daily_patterns is None:
            self.calculate_daily_patterns()

        if self.station_daily_patterns is None:
            print("无法获取充电站日分布模式")
            return

        print("正在可视化充电站日分布模式...")

        # 计算平均日分布
        avg_pattern = self.station_daily_patterns.mean()

        # 创建柱状图
        plt.figure(figsize=(10, 6))
        plt.bar(avg_pattern.index, avg_pattern.values, color='skyblue')

        plt.xlabel('星期')
        plt.ylabel('收入比例')
        plt.title('充电站平均日收入分布')
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()

        plt.savefig('output/daily_pattern_avg.png', dpi=300)
        plt.close()

        # 创建热力图
        plt.figure(figsize=(12, 10))
        sns.heatmap(self.station_daily_patterns.sample(min(30, len(self.station_daily_patterns))),
                   cmap='YlGnBu', linewidths=0.5)

        plt.xlabel('星期')
        plt.title('充电站日收入分布热力图（随机30个站）')
        plt.tight_layout()

        plt.savefig('output/daily_pattern_heatmap.png', dpi=300)
        plt.close()

        print("充电站日分布模式可视化已保存")

    def train_performance_model(self, poi_vectors):
        """训练性能预测模型"""
        if self.station_metrics is None:
            self.calculate_station_metrics()

        if self.station_metrics is None or poi_vectors is None:
            print("无法训练性能预测模型，缺少必要数据")
            return None

        print("正在训练充电站性能预测模型...")

        # 找到两个数据集中共有的站
        common_stations = list(set(self.station_metrics.index) & set(poi_vectors.index))
        print(f"找到 {len(common_stations)} 个同时有POI数据和业绩数据的充电站")

        if len(common_stations) < 10:
            print("共有站数量太少，无法训练可靠的模型")
            return None

        # 准备训练数据
        X = poi_vectors.loc[common_stations].values
        y = self.station_metrics.loc[common_stations]['total_revenue'].values

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # 训练随机森林回归模型
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)

        # 评估模型
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        print(f"模型评估结果：MSE = {mse:.2f}, R² = {r2:.2f}")

        # 保存模型
        self.performance_model = model

        # 计算特征重要性
        feature_importance = pd.DataFrame({
            'feature': poi_vectors.columns,
            'importance': model.feature_importances_
        }).sort_values('importance', ascending=False)

        # 保存特征重要性
        feature_importance.to_csv('output/feature_importance.csv', index=False)
        print("特征重要性已保存到 output/feature_importance.csv")

        # 可视化特征重要性（前20个）
        top_features = feature_importance.head(20)
        plt.figure(figsize=(12, 8))
        plt.barh(top_features['feature'], top_features['importance'], color='skyblue')
        plt.xlabel('重要性')
        plt.title('POI特征重要性（Top 20）')
        plt.gca().invert_yaxis()  # 使最重要的在顶部
        plt.grid(axis='x', linestyle='--', alpha=0.7)
        plt.tight_layout()

        plt.savefig('output/feature_importance.png', dpi=300)
        plt.close()

        print("特征重要性可视化已保存到 output/feature_importance.png")

        return self.performance_model, feature_importance

    def optimize_weights(self, poi_vectors):
        """基于业绩数据优化POI权重"""
        if self.performance_model is None:
            self.train_performance_model(poi_vectors)

        if self.performance_model is None:
            print("无法优化权重，缺少性能预测模型")
            return None

        print("正在基于业绩数据优化POI权重...")

        # 获取特征重要性
        feature_importance = pd.DataFrame({
            'feature': poi_vectors.columns,
            'importance': self.performance_model.feature_importances_
        })

        # 将特征重要性转换为权重
        # 标准化重要性值到0.5-2.0的范围
        min_importance = feature_importance['importance'].min()
        max_importance = feature_importance['importance'].max()

        if max_importance > min_importance:
            normalized_importance = 0.5 + 1.5 * (feature_importance['importance'] - min_importance) / (max_importance - min_importance)
        else:
            normalized_importance = np.ones(len(feature_importance)) * 1.0

        # 创建权重字典
        weights = dict(zip(feature_importance['feature'], normalized_importance))

        # 保存优化后的权重
        weights_df = pd.DataFrame({
            'feature': list(weights.keys()),
            'weight': list(weights.values())
        }).sort_values('weight', ascending=False)

        weights_df.to_csv('output/optimized_weights.csv', index=False)
        print("优化后的权重已保存到 output/optimized_weights.csv")

        return weights

    def predict_new_station_performance(self, new_poi_vector, poi_vectors):
        """预测新场站的业绩表现"""
        if self.performance_model is None:
            self.train_performance_model(poi_vectors)

        if self.performance_model is None:
            print("无法预测新场站业绩，缺少性能预测模型")
            return None

        print("正在预测新场站的业绩表现...")

        # 确保新场站向量与训练模型时使用的特征一致
        model_features = poi_vectors.columns

        # 调整新场站向量以匹配模型特征
        aligned_vector = pd.Series(0, index=model_features)
        for feature in new_poi_vector.index:
            if feature in aligned_vector.index:
                aligned_vector[feature] = new_poi_vector[feature]

        # 预测业绩
        predicted_revenue = self.performance_model.predict([aligned_vector.values])[0]

        print(f"预测的新场站周营业额: {predicted_revenue:.2f}")

        return predicted_revenue

    def analyze_all(self):
        """执行所有分析"""
        # 计算业绩指标
        self.calculate_station_metrics()

        # 计算时间模式
        self.calculate_hourly_patterns()
        self.calculate_daily_patterns()

        # 可视化时间模式
        self.visualize_hourly_patterns()
        self.visualize_daily_patterns()

        # 可视化表现最好的站
        self.visualize_top_stations(metric='total_revenue')
        self.visualize_top_stations(metric='equipment_utilization')
        self.visualize_top_stations(metric='revenue_per_equipment')

        # 可视化几个代表性站的业绩
        if self.station_metrics is not None and not self.station_metrics.empty:
            # 选择总收入最高的站
            top_revenue_station = self.station_metrics['total_revenue'].idxmax()
            self.visualize_station_performance(top_revenue_station)

            # 选择设备利用率最高的站
            top_utilization_station = self.station_metrics['equipment_utilization'].idxmax()
            self.visualize_station_performance(top_utilization_station)

            # 选择单设备收入最高的站
            top_efficiency_station = self.station_metrics['revenue_per_equipment'].idxmax()
            self.visualize_station_performance(top_efficiency_station)

        print("所有分析已完成")

def main():
    """主函数"""
    analyzer = OrderAnalyzer()
    analyzer.analyze_all()

if __name__ == "__main__":
    main()
